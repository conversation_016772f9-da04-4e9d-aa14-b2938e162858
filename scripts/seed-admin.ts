#!/usr/bin/env tsx

import { PrismaClient, Role, Provider } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

interface AdminUser {
	username: string;
	password: string;
	provider: Provider;
}

const DEFAULT_ADMIN_USERS: AdminUser[] = [
	{
		username: 'admin',
		password: process.env.ADMIN_DASHBOARD_PASSWORD || 'admin123',
		provider: Provider.USERNAME_PASSWORD,
	},
	{
		username: 'superadmin',
		password: process.env.SUPER_ADMIN_PASSWORD || 'superadmin123',
		provider: Provider.USERNAME_PASSWORD,
	},
];

async function createAdminUser(adminData: AdminUser): Promise<void> {
	const { username, password, provider } = adminData;

	try {
		// Check if admin user already exists
		const existingUser = await prisma.user.findUnique({
			where: { username },
		});

		if (existingUser) {
			console.log(`Admin user '${username}' already exists. Updating role to ADMIN...`);
			
			// Update existing user to admin role
			await prisma.user.update({
				where: { id: existingUser.id },
				data: { 
					role: Role.ADMIN,
					disabled: false,
				},
			});
			
			console.log(`✅ Updated user '${username}' to admin role`);
			return;
		}

		// Hash the password
		const hashedPassword = await bcrypt.hash(password, 10);

		// Create new admin user
		const adminUser = await prisma.user.create({
			data: {
				username,
				password_hash: hashedPassword,
				provider,
				provider_id: username,
				role: Role.ADMIN,
				disabled: false,
			},
		});

		console.log(`✅ Created admin user: ${adminUser.username} (ID: ${adminUser.id})`);
	} catch (error) {
		console.error(`❌ Failed to create admin user '${username}':`, error);
		throw error;
	}
}

async function seedAdminUsers(): Promise<void> {
	console.log('🌱 Starting admin user seeding...\n');

	try {
		// Connect to database
		await prisma.$connect();
		console.log('📦 Connected to database');

		// Create admin users
		for (const adminData of DEFAULT_ADMIN_USERS) {
			await createAdminUser(adminData);
		}

		console.log('\n🎉 Admin user seeding completed successfully!');
		console.log('\n📋 Admin Login Credentials:');
		console.log('================================');
		
		for (const adminData of DEFAULT_ADMIN_USERS) {
			console.log(`Username: ${adminData.username}`);
			console.log(`Password: ${adminData.password}`);
			console.log('--------------------------------');
		}

		console.log('\n⚠️  IMPORTANT SECURITY NOTES:');
		console.log('1. Change default passwords immediately after first login');
		console.log('2. Use strong passwords for production environments');
		console.log('3. Consider using environment variables for passwords');
		console.log('4. Enable 2FA if available in your deployment');

	} catch (error) {
		console.error('❌ Admin seeding failed:', error);
		process.exit(1);
	} finally {
		await prisma.$disconnect();
		console.log('\n📦 Disconnected from database');
	}
}

async function createSampleData(): Promise<void> {
	console.log('\n🌱 Creating sample data for admin dashboard...');

	try {
		// Create some sample regular users for testing
		const sampleUsers = [
			{ username: 'testuser1', provider: Provider.USERNAME_PASSWORD },
			{ username: 'testuser2', provider: Provider.USERNAME_PASSWORD },
			{ username: 'testuser3', provider: Provider.USERNAME_PASSWORD },
		];

		for (const userData of sampleUsers) {
			const existingUser = await prisma.user.findUnique({
				where: { username: userData.username },
			});

			if (!existingUser) {
				const hashedPassword = await bcrypt.hash('password123', 10);
				await prisma.user.create({
					data: {
						username: userData.username,
						password_hash: hashedPassword,
						provider: userData.provider,
						provider_id: userData.username,
						role: Role.USER,
						disabled: false,
					},
				});
				console.log(`✅ Created sample user: ${userData.username}`);
			}
		}

		// Create sample feedback
		const adminUser = await prisma.user.findFirst({
			where: { role: Role.ADMIN },
		});

		const regularUser = await prisma.user.findFirst({
			where: { role: Role.USER },
		});

		if (adminUser && regularUser) {
			const existingFeedback = await prisma.feedback.findFirst({
				where: { user_id: regularUser.id },
			});

			if (!existingFeedback) {
				await prisma.feedback.create({
					data: {
						message: 'This is a sample feedback message for testing the admin dashboard.',
						user_id: regularUser.id,
						status: 'pending',
					},
				});
				console.log('✅ Created sample feedback');
			}
		}

		console.log('✅ Sample data creation completed');
	} catch (error) {
		console.error('❌ Failed to create sample data:', error);
	}
}

// Main execution
async function main(): Promise<void> {
	const args = process.argv.slice(2);
	const includeSampleData = args.includes('--with-sample-data');

	await seedAdminUsers();

	if (includeSampleData) {
		await createSampleData();
	}
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { seedAdminUsers, createSampleData };
