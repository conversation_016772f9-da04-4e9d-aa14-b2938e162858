import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware } from './backend/middleware/auth.middleware';
import { adminPageMiddleware } from './backend/middleware/admin-page.middleware';

// Ensure middleware only runs on API routes
export const config = {
	matcher: [
		// Match all API routes
		'/api/:path*',
		// Match admin pages
		'/admin/:path*',
	],
};

export async function middleware(request: NextRequest) {
	// Skip authentication for public endpoints
	const publicPaths = [
		'/api/auth/telegram-login',
		'/api/auth/logout',
		'/api/auth/login',
		'/api/auth/google-login',
		'/api/admin/auth', // Admin login endpoint
	];

	// Admin page protection
	if (request.nextUrl.pathname.startsWith('/admin')) {
		// Allow admin login page
		if (request.nextUrl.pathname === '/admin/login') {
			return NextResponse.next();
		}

		// Use lightweight admin page middleware for basic token verification
		const adminResult = await adminPageMiddleware(request);

		// If adminResult is a Response (error), redirect to admin login
		if (adminResult instanceof Response) {
			const url = new URL('/admin/login', request.url);

			// Add error parameter based on status
			if (adminResult.status === 403) {
				url.searchParams.set('error', 'access_denied');
			} else if (adminResult.status === 401) {
				url.searchParams.set('error', 'unauthorized');
			}

			return NextResponse.redirect(url);
		}

		// Continue with the modified request
		return NextResponse.next();
	}

	// Only apply auth middleware for URLs starting with /api
	// except for the public endpoints
	if (
		request.nextUrl.pathname.startsWith('/api') &&
		!publicPaths.some((path) => request.nextUrl.pathname.startsWith(path))
	) {
		const result = await authMiddleware(request);

		// If result is a Response, it means there was an error
		if (result instanceof Response) {
			return result;
		}
	}

	const response = NextResponse.next({
		request: {
			// New request headers
			headers: request.headers,
		},
	});

	return response;
}
