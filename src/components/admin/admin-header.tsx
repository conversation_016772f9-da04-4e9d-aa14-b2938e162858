'use client';

import { But<PERSON> } from '@/components/ui';
import { Menu } from 'lucide-react';

interface AdminHeaderProps {
	onMenuClick: () => void;
	showMobileMenu?: boolean;
}

export function AdminHeader({ onMenuClick, showMobileMenu = true }: AdminHeaderProps) {
	return (
		<header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="flex h-16 items-center justify-between px-6">
				{/* Left side - Mobile menu + Logo */}
				<div className="flex items-center space-x-4">
					{showMobileMenu && (
						<Button
							variant="ghost"
							size="sm"
							className="lg:hidden"
							onClick={onMenuClick}
						>
							<Menu className="h-5 w-5" />
						</Button>
					)}

					<div className="flex items-center space-x-2">
						<span className="font-bold text-lg">Admin Panel</span>
					</div>
				</div>
			</div>
		</header>
	);
}
