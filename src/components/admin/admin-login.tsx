'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
	<PERSON>ton,
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Input,
	Label,
	LoadingSpinner,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useAdminAuth } from '@/contexts/admin-auth-context';
import { Shield, Eye, EyeOff, AlertTriangle } from 'lucide-react';

export function AdminLogin() {
	const [username, setUsername] = useState('');
	const [password, setPassword] = useState('');
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const [errorMessage, setErrorMessage] = useState('');
	const router = useRouter();
	const searchParams = useSearchParams();
	const { showSuccess, showError } = useToast();
	const { login, isAuthenticated, isAdmin } = useAdminAuth();

	// Handle error parameters from URL
	useEffect(() => {
		const error = searchParams.get('error');
		if (error) {
			switch (error) {
				case 'access_denied':
					setErrorMessage('Access denied. Admin privileges required.');
					break;
				case 'unauthorized':
					setErrorMessage('Session expired. Please log in again.');
					break;
				case 'account_disabled':
					setErrorMessage(
						'Your account has been disabled. Contact system administrator.'
					);
					break;
				default:
					setErrorMessage('Authentication failed. Please try again.');
			}
		}
	}, [searchParams]);

	// Redirect if already authenticated and admin
	useEffect(() => {
		if (isAuthenticated && isAdmin) {
			router.push('/admin');
		}
	}, [isAuthenticated, isAdmin, router]);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!username.trim() || !password.trim()) {
			showError(new Error('Please enter both username and password'));
			return;
		}

		setLoading(true);
		setErrorMessage(''); // Clear any previous errors

		try {
			await login(username.trim(), password);
			showSuccess('Login successful');
			router.push('/admin');
		} catch (error) {
			const errorMsg =
				error instanceof Error ? error.message : 'Login failed. Please try again.';
			setErrorMessage(errorMsg);
			showError(new Error(errorMsg));
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-background flex items-center justify-center p-4">
			<Card className="w-full max-w-md shadow-lg">
				<CardHeader className="text-center space-y-4">
					<div className="flex justify-center">
						<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
							<Shield className="h-6 w-6 text-primary" />
						</div>
					</div>
					<div className="space-y-2">
						<CardTitle className="text-2xl font-bold">Admin Login</CardTitle>
						<CardDescription>Sign in to access the admin dashboard</CardDescription>
					</div>
				</CardHeader>
				<CardContent>
					{/* Error Message */}
					{errorMessage && (
						<div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg flex items-start space-x-2">
							<AlertTriangle className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
							<p className="text-sm text-destructive">{errorMessage}</p>
						</div>
					)}

					<form onSubmit={handleSubmit} className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="username">Username</Label>
							<Input
								id="username"
								type="text"
								value={username}
								onChange={(e) => setUsername(e.target.value)}
								placeholder="Enter your admin username"
								disabled={loading}
								autoComplete="username"
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="password">Password</Label>
							<div className="relative">
								<Input
									id="password"
									type={showPassword ? 'text' : 'password'}
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									placeholder="Enter your password"
									disabled={loading}
									autoComplete="current-password"
									className="w-full pr-10"
								/>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
									onClick={() => setShowPassword(!showPassword)}
									disabled={loading}
									aria-label={showPassword ? 'Hide password' : 'Show password'}
									data-testid="password-toggle"
								>
									{showPassword ? (
										<EyeOff className="h-4 w-4 text-gray-400" />
									) : (
										<Eye className="h-4 w-4 text-gray-400" />
									)}
								</Button>
							</div>
						</div>

						<Button type="submit" className="w-full" disabled={loading}>
							{loading ? (
								<>
									<LoadingSpinner className="h-4 w-4 mr-2" />
									Signing in...
								</>
							) : (
								'Sign In'
							)}
						</Button>
					</form>

					<div className="mt-6 p-4 bg-amber-50 dark:bg-amber-950/20 rounded-lg border border-amber-200 dark:border-amber-800">
						<p className="text-sm font-medium text-amber-800 dark:text-amber-200">
							Default Credentials:
						</p>
						<div className="mt-2 space-y-1">
							<p className="text-xs text-amber-700 dark:text-amber-300">
								Username: admin | Password: admin123
							</p>
							<p className="text-xs text-amber-700 dark:text-amber-300">
								Username: superadmin | Password: superadmin123
							</p>
						</div>
						<p className="text-xs text-amber-600 dark:text-amber-400 mt-2 flex items-center">
							⚠️ Change these passwords in production!
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
