'use client';

import { useRouter, usePathname } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>, CardContent, Separator } from '@/components/ui';
import { Users, MessageSquare, FileText, Home, Shield, X } from 'lucide-react';

interface AdminSidebarProps {
	isOpen: boolean;
	onClose: () => void;
}

const adminNavItems = [
	{
		href: '/admin',
		label: 'Dashboard',
		icon: Home,
		description: 'Overview and system management',
	},
	{
		href: '/admin/users',
		label: 'Users',
		icon: Users,
		description: 'Manage user accounts',
	},
	{
		href: '/admin/feedback',
		label: 'Feedback',
		icon: MessageSquare,
		description: 'User feedback management',
	},
	{
		href: '/admin/audit',
		label: 'Audit Logs',
		icon: FileText,
		description: 'Security audit trail',
	},
];

export function AdminSidebar({ isOpen, onClose }: AdminSidebarProps) {
	const router = useRouter();
	const pathname = usePathname();

	const handleNavigation = (href: string) => {
		router.push(href);
		onClose();
	};

	return (
		<>
			{/* Mobile overlay */}
			{isOpen && (
				<div
					className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm lg:hidden"
					onClick={onClose}
				/>
			)}

			{/* Sidebar */}
			<aside
				className={`fixed inset-y-0 left-0 z-50 w-72 bg-background border-r transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 flex flex-col ${
					isOpen ? 'translate-x-0' : '-translate-x-full'
				}`}
			>
				{/* Navigation */}
				<nav className="flex-1 p-4 space-y-2 overflow-y-auto mt-16">
					{adminNavItems.map((item) => {
						const Icon = item.icon;
						const isActive = pathname === item.href;

						return (
							<Button
								key={item.href}
								variant={isActive ? 'default' : 'ghost'}
								className="w-full justify-start h-auto p-3"
								onClick={() => handleNavigation(item.href)}
							>
								<Icon className="h-5 w-5 mr-3 flex-shrink-0" />
								<div className="flex flex-col items-start text-left">
									<span className="font-medium">{item.label}</span>
									<span className="text-xs opacity-70 font-normal">
										{item.description}
									</span>
								</div>
							</Button>
						);
					})}
				</nav>
			</aside>
		</>
	);
}
