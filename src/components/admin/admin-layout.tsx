'use client';

import { useState } from 'react';
import { AdminGuard } from './admin-guard';
import { AdminHeader } from './admin-header';
import { AdminSidebar } from './admin-sidebar';

interface AdminLayoutProps {
	children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
	const [sidebarOpen, setSidebarOpen] = useState(false);

	return (
		<AdminGuard>
			<div className="min-h-screen bg-background flex">
				{/* Sidebar */}
				<AdminSidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

				{/* Main content */}
				<div className="flex-1 flex flex-col">
					{/* Header */}
					<AdminHeader onMenuClick={() => setSidebarOpen(true)} showMobileMenu={true} />

					{/* Page content */}
					<main className="flex-1 p-6 bg-muted/30">
						<div className="container mx-auto max-w-7xl">{children}</div>
					</main>
				</div>
			</div>
		</AdminGuard>
	);
}
