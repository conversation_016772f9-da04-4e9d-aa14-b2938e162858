'use client';

import { Button, Card, Input, Label } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useToast } from '@/contexts/toast-context';
import { Eye, EyeOff, Key, Lock } from 'lucide-react';
import { useState } from 'react';

interface ChangePasswordFormProps {
	onSuccess?: () => void;
	onCancel?: () => void;
}

export function ChangePasswordForm({ onSuccess, onCancel }: ChangePasswordFormProps) {
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();

	const [currentPassword, setCurrentPassword] = useState('');
	const [newPassword, setNewPassword] = useState('');
	const [confirmPassword, setConfirmPassword] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');

	// Password visibility states
	const [showCurrentPassword, setShowCurrentPassword] = useState(false);
	const [showNewPassword, setShowNewPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);
		setError('');

		// Client-side validation
		if (!currentPassword) {
			setError(t('auth.change_password.current_password_label') + ' is required');
			setIsLoading(false);
			return;
		}

		if (newPassword.length < 6) {
			setError(t('auth.change_password.new_password_placeholder'));
			setIsLoading(false);
			return;
		}

		if (newPassword !== confirmPassword) {
			setError(t('auth.change_password.passwords_not_match'));
			setIsLoading(false);
			return;
		}

		try {
			const response = await fetch('/api/user/change-password', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					currentPassword,
					newPassword,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || t('auth.change_password.error'));
			}

			// Success
			showSuccess(t('auth.change_password.success'));

			// Reset form
			setCurrentPassword('');
			setNewPassword('');
			setConfirmPassword('');

			// Call success callback
			onSuccess?.();
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : t('auth.change_password.error');
			setError(errorMessage);
			showError(errorMessage);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Card className="w-full max-w-md p-6">
			<div className="space-y-4">
				{/* Header */}
				<div className="text-center space-y-2">
					<div className="flex justify-center">
						<Key className="h-8 w-8 text-primary" />
					</div>
					<h2 className="text-2xl font-bold">{t('auth.change_password.title')}</h2>
					<p className="text-sm text-muted-foreground">
						{t('auth.change_password.description')}
					</p>
				</div>

				{/* Form */}
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* Current Password */}
					<div className="space-y-2">
						<Label htmlFor="current-password">
							{t('auth.change_password.current_password_label')}
						</Label>
						<div className="relative">
							<Input
								id="current-password"
								type={showCurrentPassword ? 'text' : 'password'}
								value={currentPassword}
								onChange={(e) => setCurrentPassword(e.target.value)}
								placeholder={t('auth.change_password.current_password_placeholder')}
								required
								disabled={isLoading}
								className="pr-10"
							/>
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowCurrentPassword(!showCurrentPassword)}
								disabled={isLoading}
							>
								{showCurrentPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
					</div>

					{/* New Password */}
					<div className="space-y-2">
						<Label htmlFor="new-password">
							{t('auth.change_password.new_password_label')}
						</Label>
						<div className="relative">
							<Input
								id="new-password"
								type={showNewPassword ? 'text' : 'password'}
								value={newPassword}
								onChange={(e) => setNewPassword(e.target.value)}
								placeholder={t('auth.change_password.new_password_placeholder')}
								required
								disabled={isLoading}
								className="pr-10"
							/>
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowNewPassword(!showNewPassword)}
								disabled={isLoading}
							>
								{showNewPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
					</div>

					{/* Confirm Password */}
					<div className="space-y-2">
						<Label htmlFor="confirm-password">
							{t('auth.change_password.confirm_password_label')}
						</Label>
						<div className="relative">
							<Input
								id="confirm-password"
								type={showConfirmPassword ? 'text' : 'password'}
								value={confirmPassword}
								onChange={(e) => setConfirmPassword(e.target.value)}
								placeholder={t('auth.change_password.confirm_password_placeholder')}
								required
								disabled={isLoading}
								className="pr-10"
							/>
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowConfirmPassword(!showConfirmPassword)}
								disabled={isLoading}
							>
								{showConfirmPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
					</div>

					{/* Error Message */}
					{error && (
						<div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
							{error}
						</div>
					)}

					{/* Action Buttons */}
					<div className="flex gap-2 pt-2">
						{onCancel && (
							<Button
								type="button"
								variant="outline"
								onClick={onCancel}
								disabled={isLoading}
								className="flex-1"
							>
								Cancel
							</Button>
						)}
						<Button type="submit" disabled={isLoading} className="flex-1">
							{isLoading ? (
								<>
									<Lock className="h-4 w-4 mr-2 animate-spin" />
									Changing...
								</>
							) : (
								<>
									<Key className="h-4 w-4 mr-2" />
									{t('auth.change_password.submit')}
								</>
							)}
						</Button>
					</div>
				</form>
			</div>
		</Card>
	);
}
