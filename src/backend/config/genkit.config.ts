import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { getLLMConfig } from '@/config';

let genkitInitialized = false;
let genkitInstance: any = null;

/**
 * Initialize Genkit with Google AI plugin
 * This should be called once during application startup
 */
export async function initializeGenkit(): Promise<void> {
	if (genkitInitialized) {
		return;
	}

	const llmConfig = await getLLMConfig();
	
	if (!llmConfig.geminiKey) {
		console.warn('Gemini API key not configured, Genkit will not be initialized');
		return;
	}

	try {
		genkitInstance = genkit({
			plugins: [
				googleAI({ 
					apiKey: llmConfig.geminiKey,
				})
			],
		});

		genkitInitialized = true;
		console.log('Genkit initialized successfully with Google AI plugin');
	} catch (error) {
		console.error('Failed to initialize Genkit:', error);
		throw error;
	}
}

/**
 * Check if Genkit is initialized
 */
export function isGenkitInitialized(): boolean {
	return genkitInitialized;
}

/**
 * Get the Genkit instance
 */
export function getGenkitInstance() {
	return genkitInstance;
}

/**
 * Reset Genkit initialization state (for testing)
 */
export function resetGenkitInitialization(): void {
	genkitInitialized = false;
	genkitInstance = null;
}

/**
 * Get Genkit configuration for different environments
 */
export function getGenkitConfig() {
	return {
		development: {
			logLevel: 'debug' as const,
			enableTracingAndMetrics: true,
			telemetry: {
				instrumentation: 'googleCloud' as const,
				logger: 'googleCloud' as const,
			},
		},
		production: {
			logLevel: 'info' as const,
			enableTracingAndMetrics: false,
			telemetry: {
				instrumentation: 'none' as const,
				logger: 'none' as const,
			},
		},
		test: {
			logLevel: 'error' as const,
			enableTracingAndMetrics: false,
			telemetry: {
				instrumentation: 'none' as const,
				logger: 'none' as const,
			},
		},
	};
}

/**
 * Model configuration for different Gemini models
 */
export const GENKIT_MODELS = {
	'gemini-1.5-flash': {
		name: 'gemini-1.5-flash',
		maxTokens: 1000000,
		costPer1kInput: 0.000075,
		costPer1kOutput: 0.0003,
		capabilities: ['text-generation', 'translation', 'evaluation'],
		recommended: ['quick-tasks', 'high-volume', 'cost-sensitive'],
	},
	'gemini-1.5-pro': {
		name: 'gemini-1.5-pro',
		maxTokens: 2000000,
		costPer1kInput: 0.00125,
		costPer1kOutput: 0.005,
		capabilities: ['text-generation', 'translation', 'evaluation', 'creative', 'analytical'],
		recommended: ['complex-reasoning', 'high-quality', 'analytical-tasks'],
	},
	'gemini-pro': {
		name: 'gemini-pro',
		maxTokens: 32768,
		costPer1kInput: 0.0005,
		costPer1kOutput: 0.0015,
		capabilities: ['text-generation', 'translation', 'evaluation', 'creative'],
		recommended: ['general-purpose', 'balanced-performance'],
	},
} as const;

/**
 * Get model configuration by name
 */
export function getModelConfig(modelName: string) {
	return GENKIT_MODELS[modelName as keyof typeof GENKIT_MODELS] || null;
}

/**
 * Get recommended model for a specific task type
 */
export function getRecommendedModel(taskType: string): string {
	const taskModelMap: Record<string, string> = {
		'quick-generation': 'gemini-1.5-flash',
		'high-volume': 'gemini-1.5-flash',
		'cost-sensitive': 'gemini-1.5-flash',
		'complex-reasoning': 'gemini-1.5-pro',
		'analytical': 'gemini-1.5-pro',
		'high-quality': 'gemini-1.5-pro',
		'general': 'gemini-pro',
		'balanced': 'gemini-pro',
	};

	return taskModelMap[taskType] || 'gemini-1.5-flash';
}
