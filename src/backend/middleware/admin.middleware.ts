import { jwtVerify, type J<PERSON><PERSON>ayload } from 'jose';
import { NextRequest, NextResponse } from 'next/server';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api-error-middleware';

/**
 * Verifies a JWT token and checks admin role in middleware context
 */
async function verifyAdminTokenInMiddleware(token: string, secret: string): Promise<JWTPayload> {
	try {
		const { payload } = await jwtVerify<JWTPayload>(token, new TextEncoder().encode(secret));
		return payload;
	} catch {
		throw new Error('Invalid token');
	}
}

/**
 * Lightweight admin authentication middleware for Next.js middleware
 * Only verifies JWT token - detailed role checking happens in API routes
 */
export async function adminMiddleware(request: NextRequest) {
	const jwtCookieName = process.env.JWT_COOKIE_NAME;
	const jwtSecret = process.env.JWT_SECRET;

	if (!jwtCookieName?.length || !jwtSecret?.length) {
		console.error('JWT_COOKIE_NAME or JWT_SECRET is not defined in environment variables');
		return Response.json({ error: 'Internal server error' }, { status: 500 });
	}

	// Extract token from cookie
	const token = request.cookies.get(jwtCookieName)?.value;

	if (!token) {
		return Response.json({ error: 'Unauthorized - No token provided' }, { status: 401 });
	}

	try {
		// Verify and decode token
		const payload = await verifyAdminTokenInMiddleware(token, jwtSecret);
		const userId = payload.sub;

		if (!userId) {
			return Response.json(
				{ error: 'Unauthorized - Invalid user ID in token' },
				{ status: 401 }
			);
		}

		// Add user context to request headers for API routes to use
		request.headers.set('x-user-id', userId);

		return request;
	} catch (error) {
		console.error('Admin token verification failed:', error);
		return Response.json({ error: 'Unauthorized - Invalid token' }, { status: 401 });
	}
}

/**
 * Helper function to check if a user has admin role (for use in API routes)
 * Note: This should be implemented in API routes using getUserService from wire.ts
 * to avoid circular dependencies in middleware
 */

/**
 * Middleware wrapper for API routes that require admin access
 * This performs full admin role checking using the database
 */
export function withAdminAuth(handler: ApiHandler): ApiHandler {
	return async (request: NextRequest, context?: any): Promise<NextResponse> => {
		// Import getUserService here to avoid circular dependencies
		const { getUserService } = await import('@/backend/wire');

		const jwtCookieName = process.env.JWT_COOKIE_NAME;
		const jwtSecret = process.env.JWT_SECRET;

		if (!jwtCookieName?.length || !jwtSecret?.length) {
			return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
		}

		// Extract token from cookie
		const token = request.cookies.get(jwtCookieName)?.value;

		if (!token) {
			return NextResponse.json(
				{ error: 'Unauthorized - No token provided' },
				{ status: 401 }
			);
		}

		try {
			// Verify and decode token
			const payload = await verifyAdminTokenInMiddleware(token, jwtSecret);
			const userId = payload.sub;

			if (!userId) {
				return NextResponse.json(
					{ error: 'Unauthorized - Invalid user ID in token' },
					{ status: 401 }
				);
			}

			// Check if user exists and has admin role
			const userService = getUserService();
			const user = await userService.getUserById(userId);

			if (!user) {
				return NextResponse.json(
					{ error: 'Unauthorized - User not found' },
					{ status: 401 }
				);
			}

			const { Role } = await import('@prisma/client');
			if (user.role !== Role.ADMIN) {
				return NextResponse.json(
					{ error: 'Forbidden - Admin access required' },
					{ status: 403 }
				);
			}

			if (user.disabled) {
				return NextResponse.json(
					{ error: 'Forbidden - Account disabled' },
					{ status: 403 }
				);
			}

			// Add user context to request headers
			request.headers.set('x-user-id', userId);
			request.headers.set('x-user-role', user.role);

			// Proceed with handler
			const result = await handler(request, context);

			// Ensure we return a NextResponse
			if (result instanceof NextResponse) {
				return result;
			}

			// This shouldn't happen with ApiHandler, but just in case
			return result as NextResponse;
		} catch (error) {
			console.error('Admin auth check failed:', error);
			return NextResponse.json({ error: 'Unauthorized - Invalid token' }, { status: 401 });
		}
	};
}
