import { Provider, User } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { UserService } from './user.service';
import { AuditService } from './audit.service';

export interface AuthService {
	providerLogin(
		provider: Provider,
		provider_id: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User>;
	usernamePasswordLogin(
		username: string,
		password: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User>;
	changePassword(
		userId: string,
		currentPassword: string,
		newPassword: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void>;
	getUserById(userId: string): Promise<User | null>;
	findOrCreateUserByProvider(
		provider: Provider,
		providerId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User>;
	logout(userId: string, ipAddress?: string, userAgent?: string): Promise<void>;
}

export class AuthServiceImpl implements AuthService {
	constructor(
		private readonly getUserService: () => UserService,
		private readonly getAuditService: () => AuditService
	) {}

	async providerLogin(
		provider: Provider,
		provider_id: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, provider_id);
		let isNewUser = false;

		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id,
			});
			isNewUser = true;
		}

		// Log audit event
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: isNewUser ? 'REGISTER' : 'LOGIN',
				resource: 'user',
				resource_id: user.id,
				user_id: user.id,
				details: {
					provider,
					provider_id,
					isNewUser,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for provider login:', error);
		}

		return user;
	}

	async usernamePasswordLogin(
		username: string,
		password: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User> {
		let user = await this.getUserService().getUserByUsername(username);
		let isNewUser = false;

		if (!user) {
			// Tạo user mới nếu chưa tồn tại
			const hashedPassword = await bcrypt.hash(password, 10);
			user = await this.getUserService().createUserWithPassword({
				username,
				password_hash: hashedPassword,
				provider: Provider.USERNAME_PASSWORD,
				provider_id: username, // Sử dụng username làm provider_id
			});
			isNewUser = true;
		} else {
			// Kiểm tra mật khẩu nếu user đã tồn tại
			if (!user.password_hash) {
				throw new Error('User exists but has no password set');
			}

			const isValidPassword = await bcrypt.compare(password, user.password_hash);
			if (!isValidPassword) {
				// Log failed login attempt
				try {
					const auditService = this.getAuditService();
					await auditService.logEvent({
						action: 'LOGIN_FAILED',
						resource: 'user',
						resource_id: user.id,
						user_id: user.id,
						details: {
							username,
							reason: 'Invalid password',
						},
						ip_address: ipAddress,
						user_agent: userAgent,
					});
				} catch (error) {
					console.error('Failed to log audit event for failed login:', error);
				}
				throw new Error('Invalid password');
			}
		}

		// Log successful login/registration
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: isNewUser ? 'REGISTER' : 'LOGIN',
				resource: 'user',
				resource_id: user.id,
				user_id: user.id,
				details: {
					username,
					provider: Provider.USERNAME_PASSWORD,
					isNewUser,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for username/password login:', error);
		}

		return user;
	}

	async changePassword(
		userId: string,
		currentPassword: string,
		newPassword: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void> {
		// Get user by ID
		const user = await this.getUserService().getUserById(userId);
		if (!user) {
			throw new Error('User not found');
		}

		// Check if user has a password (only for USERNAME_PASSWORD provider)
		if (user.provider !== Provider.USERNAME_PASSWORD || !user.password_hash) {
			throw new Error('Password change not supported for this account type');
		}

		// Verify current password
		const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
		if (!isCurrentPasswordValid) {
			// Log failed password change attempt
			try {
				const auditService = this.getAuditService();
				await auditService.logEvent({
					action: 'CHANGE_PASSWORD_FAILED',
					resource: 'user',
					resource_id: userId,
					user_id: userId,
					details: {
						reason: 'Invalid current password',
					},
					ip_address: ipAddress,
					user_agent: userAgent,
				});
			} catch (error) {
				console.error('Failed to log audit event for failed password change:', error);
			}
			throw new Error('Current password is incorrect');
		}

		// Validate new password
		if (newPassword.length < 6) {
			throw new Error('New password must be at least 6 characters long');
		}

		// Hash new password
		const hashedNewPassword = await bcrypt.hash(newPassword, 10);

		// Update password in database
		await this.getUserService().updatePassword(userId, hashedNewPassword);

		// Log successful password change
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: 'CHANGE_PASSWORD',
				resource: 'user',
				resource_id: userId,
				user_id: userId,
				details: {
					success: true,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for password change:', error);
		}
	}

	async getUserById(userId: string): Promise<User | null> {
		return this.getUserService().getUserById(userId);
	}

	async findOrCreateUserByProvider(
		provider: Provider,
		providerId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, providerId);
		let isNewUser = false;

		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id: providerId,
			});
			isNewUser = true;
		}

		// Log audit event
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: isNewUser ? 'REGISTER' : 'ACCESS',
				resource: 'user',
				resource_id: user.id,
				user_id: user.id,
				details: {
					provider,
					provider_id: providerId,
					isNewUser,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for findOrCreateUserByProvider:', error);
		}

		return user;
	}

	async logout(userId: string, ipAddress?: string, userAgent?: string): Promise<void> {
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: 'LOGOUT',
				resource: 'user',
				resource_id: userId,
				user_id: userId,
				details: {},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for logout:', error);
		}
	}
}
