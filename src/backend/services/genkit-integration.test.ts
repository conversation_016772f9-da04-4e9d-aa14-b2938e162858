import { GeminiService } from './gemini.service';

// Mock Genkit
jest.mock('genkit', () => ({
	generate: jest.fn(),
}));

jest.mock('@genkit-ai/googleai', () => ({
	gemini15Flash: 'gemini-1.5-flash',
	gemini15Pro: 'gemini-1.5-pro',
	geminiPro: 'gemini-pro',
}));

jest.mock('@/backend/config/genkit.config', () => ({
	initializeGenkit: jest.fn().mockResolvedValue(undefined),
	isGenkitInitialized: jest.fn().mockReturnValue(true),
}));

// Mock config
jest.mock('@/config', () => ({
	getLLMConfig: jest.fn().mockResolvedValue({
		geminiKey: 'test-gemini-key',
		geminiModel: 'gemini-1.5-flash',
		openAIKey: 'test-openai-key',
		openAIModel: 'gpt-4o-mini',
		defaultProvider: 'gemini',
		maxExamples: 8,
		temperature: 0.7,
		maxTokens: 1000,
	}),
}));

describe('Genkit Integration Tests', () => {
	let geminiService: GeminiService;
	let mockGenerate: jest.MockedFunction<any>;

	beforeEach(async () => {
		jest.clearAllMocks();
		
		// Get the mocked generate function
		const { generate } = require('genkit');
		mockGenerate = generate as jest.MockedFunction<any>;
		
		geminiService = new GeminiService();
		
		// Wait for initialization to complete
		await new Promise(resolve => setTimeout(resolve, 10));
	});

	describe('Genkit Framework Integration', () => {
		it('should initialize Genkit correctly', async () => {
			// Arrange
			const { initializeGenkit } = require('@/backend/config/genkit.config');
			
			// Act & Assert
			expect(initializeGenkit).toHaveBeenCalled();
		});

		it('should use correct Genkit model selection', async () => {
			// Arrange
			const mockResponse = {
				text: () => 'Test response from Genkit',
				usage: {
					inputTokens: 10,
					outputTokens: 5,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act
			const result = await geminiService.createChatCompletion({
				messages: [{ role: 'user', content: 'Test message' }],
				model: 'gemini-1.5-pro',
				temperature: 0.8,
				maxTokens: 500,
			});

			// Assert
			expect(mockGenerate).toHaveBeenCalledWith({
				model: 'gemini-1.5-pro',
				prompt: 'User: Test message',
				config: {
					temperature: 0.8,
					maxOutputTokens: 500,
				},
			});

			expect(result).toEqual({
				content: 'Test response from Genkit',
				usage: {
					promptTokens: 10,
					completionTokens: 5,
					totalTokens: 15,
				},
			});
		});

		it('should handle Genkit model fallback', async () => {
			// Arrange
			const mockResponse = {
				text: () => 'Fallback response',
				usage: {
					inputTokens: 8,
					outputTokens: 4,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act - Use unknown model, should fallback to default
			const result = await geminiService.createChatCompletion({
				messages: [{ role: 'user', content: 'Test message' }],
				model: 'unknown-model',
				temperature: 0.7,
			});

			// Assert - Should use default model (gemini-1.5-flash)
			expect(mockGenerate).toHaveBeenCalledWith({
				model: 'gemini-1.5-flash',
				prompt: 'User: Test message',
				config: {
					temperature: 0.7,
					maxOutputTokens: 1000,
				},
			});
		});

		it('should handle Genkit structured output', async () => {
			// Arrange
			const mockData = { name: 'John', age: 30, city: 'New York' };
			const mockResponse = {
				text: () => JSON.stringify(mockData),
				usage: {
					inputTokens: 20,
					outputTokens: 15,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			const schema = require('zod').z.object({
				name: require('zod').z.string(),
				age: require('zod').z.number(),
				city: require('zod').z.string(),
			});

			// Act
			const result = await geminiService.createStructuredCompletion({
				messages: [{ role: 'user', content: 'Generate user data' }],
			}, schema);

			// Assert
			expect(result.data).toEqual(mockData);
			expect(result.usage).toEqual({
				promptTokens: 20,
				completionTokens: 15,
				totalTokens: 35,
			});
		});

		it('should handle Genkit errors gracefully', async () => {
			// Arrange
			const genkitError = new Error('Genkit service unavailable');
			mockGenerate.mockRejectedValue(genkitError);

			// Act & Assert
			await expect(geminiService.createChatCompletion({
				messages: [{ role: 'user', content: 'Test message' }],
			})).rejects.toThrow('Genkit API call failed: Genkit service unavailable');
		});

		it('should support different Genkit models', async () => {
			// Test all supported models
			const models = [
				{ input: 'gemini-1.5-flash', expected: 'gemini-1.5-flash' },
				{ input: 'gemini-1.5-pro', expected: 'gemini-1.5-pro' },
				{ input: 'gemini-pro', expected: 'gemini-pro' },
			];

			for (const { input, expected } of models) {
				// Arrange
				const mockResponse = {
					text: () => `Response from ${input}`,
					usage: { inputTokens: 5, outputTokens: 3 },
				};

				mockGenerate.mockResolvedValue(mockResponse);

				// Act
				await geminiService.createChatCompletion({
					messages: [{ role: 'user', content: 'Test' }],
					model: input,
				});

				// Assert
				expect(mockGenerate).toHaveBeenCalledWith(
					expect.objectContaining({
						model: expected,
					})
				);

				mockGenerate.mockClear();
			}
		});
	});

	describe('Genkit Performance Features', () => {
		it('should handle Genkit usage tracking', async () => {
			// Arrange
			const mockResponse = {
				text: () => 'Performance test response',
				usage: {
					inputTokens: 100,
					outputTokens: 50,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act
			const result = await geminiService.createChatCompletion({
				messages: [{ role: 'user', content: 'Performance test message' }],
			});

			// Assert - Verify usage tracking
			expect(result.usage).toBeDefined();
			expect(result.usage?.promptTokens).toBe(100);
			expect(result.usage?.completionTokens).toBe(50);
			expect(result.usage?.totalTokens).toBe(150);
		});

		it('should handle Genkit configuration options', async () => {
			// Arrange
			const mockResponse = {
				text: () => 'Config test response',
				usage: { inputTokens: 10, outputTokens: 5 },
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act
			await geminiService.createChatCompletion({
				messages: [{ role: 'user', content: 'Config test' }],
				temperature: 0.9,
				maxTokens: 2000,
			});

			// Assert - Verify configuration is passed correctly
			expect(mockGenerate).toHaveBeenCalledWith({
				model: 'gemini-1.5-flash',
				prompt: 'User: Config test',
				config: {
					temperature: 0.9,
					maxOutputTokens: 2000,
				},
			});
		});
	});

	describe('Genkit Integration with LLMService', () => {
		it('should integrate seamlessly with existing LLMService', async () => {
			// This test verifies that GeminiService can be used as a drop-in replacement
			// for the previous Google AI SDK implementation
			
			// Arrange
			const mockResponse = {
				text: () => 'LLMService integration test',
				usage: { inputTokens: 15, outputTokens: 8 },
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act - Simulate LLMService usage pattern
			const result = await geminiService.createChatCompletion({
				messages: [
					{ role: 'system', content: 'You are a helpful assistant' },
					{ role: 'user', content: 'Hello' },
				],
				temperature: 0.7,
				maxTokens: 1000,
			});

			// Assert
			expect(result.content).toBe('LLMService integration test');
			expect(result.usage).toBeDefined();
			
			// Verify the prompt conversion works correctly
			expect(mockGenerate).toHaveBeenCalledWith({
				model: 'gemini-1.5-flash',
				prompt: 'System: You are a helpful assistant\n\nUser: Hello',
				config: {
					temperature: 0.7,
					maxOutputTokens: 1000,
				},
			});
		});
	});
});
