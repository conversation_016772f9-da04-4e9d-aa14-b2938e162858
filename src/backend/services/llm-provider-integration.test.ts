// Mock server-side dependencies first
jest.mock('@/backend/cache-init.server', () => ({
	getCacheService: jest.fn().mockResolvedValue({
		get: jest.fn(),
		set: jest.fn(),
		del: jest.fn(),
		flush: jest.fn(),
		getStats: jest.fn(),
		invalidateByTag: jest.fn(),
		generateLLMKey: jest.fn(),
		generateKey: jest.fn(),
	}),
}));

jest.mock('./semantic-cache.service', () => ({
	semanticCache: {
		getWithSemantic: jest.fn(),
		setWithSemantic: jest.fn(),
		generateLLMKey: jest.fn(),
	},
}));

jest.mock('./batch-processor.service', () => ({
	batchProcessor: {
		addRequest: jest.fn(),
	},
}));

jest.mock('./model-selector.service', () => ({
	modelSelector: {
		selectModel: jest.fn().mockReturnValue({
			model: 'gpt-4o-mini',
			confidence: 0.9,
			reasoning: 'Test reasoning',
		}),
		updateModelPerformance: jest.fn(),
	},
}));

jest.mock('./token-monitor.service', () => ({
	tokenMonitor: {
		trackUsage: jest.fn(),
	},
}));

jest.mock('./prompt-optimizer.service', () => ({
	PromptOptimizerService: {
		optimizePrompt: jest.fn().mockReturnValue({
			optimizedPrompt: 'optimized prompt',
			compressionRatio: 0.8,
		}),
		estimateTokens: jest.fn().mockReturnValue(100),
	},
}));

import { LLMService } from './llm.service';

// Mock both providers
const mockOpenAICreate = jest.fn();
const mockGeminiGenerate = jest.fn();

jest.mock('openai', () => {
	const mockOpenAI = jest.fn().mockImplementation(() => ({
		chat: {
			completions: {
				create: mockOpenAICreate,
			},
		},
	}));
	return {
		__esModule: true,
		default: mockOpenAI,
		OpenAI: mockOpenAI,
	};
});

jest.mock('@google/generative-ai', () => {
	const mockGoogleAI = jest.fn().mockImplementation(() => ({
		getGenerativeModel: jest.fn().mockReturnValue({
			generateContent: mockGeminiGenerate,
		}),
	}));
	return {
		__esModule: true,
		GoogleGenerativeAI: mockGoogleAI,
	};
});

// Mock WordService
const mockWordService = {
	getWordsByTerms: jest.fn().mockResolvedValue([]),
	getWordsByCollection: jest.fn().mockResolvedValue([]),
};

describe('LLM Provider Integration', () => {
	let llmService: LLMService;

	beforeEach(async () => {
		jest.clearAllMocks();

		// Reset all mocks
		mockOpenAICreate.mockReset();
		mockGeminiGenerate.mockReset();

		llmService = new LLMService(() => mockWordService as any);

		// Wait for initialization
		await new Promise((resolve) => setTimeout(resolve, 10));
	});

	describe('Provider Selection Logic', () => {
		it('should use OpenAI when defaultProvider is openai', async () => {
			// Mock config for OpenAI
			jest.doMock('@/config', () => ({
				getLLMConfig: jest.fn().mockResolvedValue({
					openAIKey: 'test-openai-key',
					openAIModel: 'gpt-4o-mini',
					geminiKey: 'test-gemini-key',
					geminiModel: 'gemini-1.5-flash',
					defaultProvider: 'openai',
					maxExamples: 8,
					temperature: 0.7,
					maxTokens: 1000,
				}),
				getLLMOptimizationConfig: jest.fn().mockResolvedValue({
					enabled: false,
					promptOptimization: { enabled: false },
					caching: { enabled: false },
					batchProcessing: { enabled: false },
				}),
			}));

			// Setup OpenAI mock response
			mockOpenAICreate.mockResolvedValue({
				choices: [{ message: { content: 'OpenAI response' } }],
				usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 },
			});

			// Test the makeLLMCall method
			const result = await (llmService as any).makeLLMCall({
				messages: [{ role: 'user', content: 'Test message' }],
				temperature: 0.7,
				maxTokens: 100,
			});

			// Verify OpenAI was called
			expect(mockOpenAICreate).toHaveBeenCalled();
			expect(mockGeminiGenerate).not.toHaveBeenCalled();
			expect(result.content).toBe('OpenAI response');
		});

		it('should use Gemini when defaultProvider is gemini', async () => {
			// Mock config for Gemini
			jest.doMock('@/config', () => ({
				getLLMConfig: jest.fn().mockResolvedValue({
					openAIKey: 'test-openai-key',
					openAIModel: 'gpt-4o-mini',
					geminiKey: 'test-gemini-key',
					geminiModel: 'gemini-1.5-flash',
					defaultProvider: 'gemini',
					maxExamples: 8,
					temperature: 0.7,
					maxTokens: 1000,
				}),
				getLLMOptimizationConfig: jest.fn().mockResolvedValue({
					enabled: false,
					promptOptimization: { enabled: false },
					caching: { enabled: false },
					batchProcessing: { enabled: false },
				}),
			}));

			// Setup Gemini mock response
			mockGeminiGenerate.mockResolvedValue({
				response: {
					text: () => 'Gemini response',
					usageMetadata: {
						promptTokenCount: 10,
						candidatesTokenCount: 5,
						totalTokenCount: 15,
					},
				},
			});

			// Test the makeLLMCall method
			const result = await (llmService as any).makeLLMCall({
				messages: [{ role: 'user', content: 'Test message' }],
				temperature: 0.7,
				maxTokens: 100,
			});

			// Verify Gemini was called
			expect(mockGeminiGenerate).toHaveBeenCalled();
			expect(mockOpenAICreate).not.toHaveBeenCalled();
			expect(result.content).toBe('Gemini response');
		});

		it('should use Gemini for gemini models regardless of defaultProvider', async () => {
			// Mock config with OpenAI as default
			jest.doMock('@/config', () => ({
				getLLMConfig: jest.fn().mockResolvedValue({
					openAIKey: 'test-openai-key',
					openAIModel: 'gpt-4o-mini',
					geminiKey: 'test-gemini-key',
					geminiModel: 'gemini-1.5-flash',
					defaultProvider: 'openai',
					maxExamples: 8,
					temperature: 0.7,
					maxTokens: 1000,
				}),
				getLLMOptimizationConfig: jest.fn().mockResolvedValue({
					enabled: false,
					promptOptimization: { enabled: false },
					caching: { enabled: false },
					batchProcessing: { enabled: false },
				}),
			}));

			// Setup Gemini mock response
			mockGeminiGenerate.mockResolvedValue({
				response: {
					text: () => 'Gemini model response',
					usageMetadata: {
						promptTokenCount: 10,
						candidatesTokenCount: 5,
						totalTokenCount: 15,
					},
				},
			});

			// Test with a Gemini model
			const result = await (llmService as any).makeLLMCall({
				messages: [{ role: 'user', content: 'Test message' }],
				model: 'gemini-1.5-pro',
				temperature: 0.7,
				maxTokens: 100,
			});

			// Verify Gemini was called even though default is OpenAI
			expect(mockGeminiGenerate).toHaveBeenCalled();
			expect(mockOpenAICreate).not.toHaveBeenCalled();
			expect(result.content).toBe('Gemini model response');
		});

		it('should fallback to OpenAI when Gemini is not available', async () => {
			// Mock config with no Gemini key
			jest.doMock('@/config', () => ({
				getLLMConfig: jest.fn().mockResolvedValue({
					openAIKey: 'test-openai-key',
					openAIModel: 'gpt-4o-mini',
					geminiKey: '', // No Gemini key
					geminiModel: 'gemini-1.5-flash',
					defaultProvider: 'gemini',
					maxExamples: 8,
					temperature: 0.7,
					maxTokens: 1000,
				}),
				getLLMOptimizationConfig: jest.fn().mockResolvedValue({
					enabled: false,
					promptOptimization: { enabled: false },
					caching: { enabled: false },
					batchProcessing: { enabled: false },
				}),
			}));

			// Setup OpenAI mock response
			mockOpenAICreate.mockResolvedValue({
				choices: [{ message: { content: 'Fallback to OpenAI' } }],
				usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 },
			});

			// Test fallback behavior
			const result = await (llmService as any).makeLLMCall({
				messages: [{ role: 'user', content: 'Test message' }],
				temperature: 0.7,
				maxTokens: 100,
			});

			// Verify OpenAI was used as fallback
			expect(mockOpenAICreate).toHaveBeenCalled();
			expect(result.content).toBe('Fallback to OpenAI');
		});

		it('should throw error when no providers are available', async () => {
			// Mock config with no API keys
			jest.doMock('@/config', () => ({
				getLLMConfig: jest.fn().mockResolvedValue({
					openAIKey: '', // No OpenAI key
					openAIModel: 'gpt-4o-mini',
					geminiKey: '', // No Gemini key
					geminiModel: 'gemini-1.5-flash',
					defaultProvider: 'openai',
					maxExamples: 8,
					temperature: 0.7,
					maxTokens: 1000,
				}),
				getLLMOptimizationConfig: jest.fn().mockResolvedValue({
					enabled: false,
					promptOptimization: { enabled: false },
					caching: { enabled: false },
					batchProcessing: { enabled: false },
				}),
			}));

			// Test error when no providers available
			await expect(
				(llmService as any).makeLLMCall({
					messages: [{ role: 'user', content: 'Test message' }],
					temperature: 0.7,
					maxTokens: 100,
				})
			).rejects.toThrow('No available LLM provider configured');
		});
	});

	describe('Response Format Compatibility', () => {
		it('should return consistent response format from both providers', async () => {
			const testMessage = { role: 'user' as const, content: 'Test message' };

			// Test OpenAI response format
			mockOpenAICreate.mockResolvedValue({
				choices: [{ message: { content: 'OpenAI response' } }],
				usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 },
			});

			const openaiResult = await (llmService as any).makeLLMCall({
				messages: [testMessage],
			});

			// Test Gemini response format
			mockGeminiGenerate.mockResolvedValue({
				response: {
					text: () => 'Gemini response',
					usageMetadata: {
						promptTokenCount: 10,
						candidatesTokenCount: 5,
						totalTokenCount: 15,
					},
				},
			});

			const geminiResult = await (llmService as any).makeLLMCall({
				messages: [testMessage],
			});

			// Both should return the same structure
			expect(openaiResult).toHaveProperty('content');
			expect(openaiResult).toHaveProperty('usage');
			expect(geminiResult).toHaveProperty('content');
			expect(geminiResult).toHaveProperty('usage');

			expect(typeof openaiResult.content).toBe('string');
			expect(typeof geminiResult.content).toBe('string');
		});
	});
});
