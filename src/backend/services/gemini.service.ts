import { googleA<PERSON> } from '@genkit-ai/googleai';
import { genkit } from 'genkit';
import { getLLMConfig } from '@/config';
import { initializeGenkit } from '@/backend/config/genkit.config';
import { createErrorContext, errorLogger, normalizeError, ServerError } from '@/lib/error-handling';
import { z } from 'zod';

export interface GeminiResponse {
	content: string;
	usage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export interface GeminiCompletionParams {
	model?: string;
	messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>;
	temperature?: number;
	maxTokens?: number;
	responseFormat?: any; // For structured output
}

export class GeminiService {
	private initialized = false;
	private initPromise: Promise<void> | null = null;
	private ai: any = null;

	constructor() {
		this.initPromise = this.initializeGenkit();
	}

	private async initializeGenkit(): Promise<void> {
		// Use centralized Genkit initialization
		await initializeGenkit();

		// Initialize Genkit AI instance
		this.ai = genkit({
			plugins: [googleAI()],
		});

		this.initialized = true;
	}

	private async ensureInitialized(): Promise<any> {
		if (this.initPromise) {
			await this.initPromise;
			this.initPromise = null;
		}
		if (!this.initialized || !this.ai) {
			const error = new ServerError(
				'Genkit service not initialized',
				createErrorContext('GeminiService', 'ensureInitialized')
			);
			errorLogger.error('Genkit service initialization failed', error);
			throw error;
		}
		return this.ai;
	}

	/**
	 * Create a chat completion using Gemini
	 */
	async createChatCompletion(params: GeminiCompletionParams): Promise<GeminiResponse> {
		const ai = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();

		// Select the appropriate model using string identifier
		const modelName = params.model || llmConfig.geminiModel;
		const modelId = `googleai/${modelName}`;

		// Convert OpenAI-style messages to a single prompt
		const prompt = this.convertMessagesToPrompt(params.messages);

		try {
			const result = await ai.generate({
				model: modelId,
				prompt,
				config: {
					temperature: params.temperature ?? 0.7,
					maxOutputTokens: params.maxTokens ?? 1000,
				},
			});

			const text = result.text;

			// Handle structured output if responseFormat is specified
			let finalContent = text;
			if (params.responseFormat) {
				try {
					// Try to parse as JSON if structured output is requested
					const parsed = JSON.parse(text);
					finalContent = JSON.stringify(parsed);
				} catch {
					// If parsing fails, return as-is
					finalContent = text;
				}
			}

			return {
				content: finalContent,
				usage: {
					promptTokens: result.usage?.inputTokens || 0,
					completionTokens: result.usage?.outputTokens || 0,
					totalTokens:
						(result.usage?.inputTokens || 0) + (result.usage?.outputTokens || 0),
				},
			};
		} catch (error) {
			const context = createErrorContext('GeminiService', 'createChatCompletion', {
				model: modelName,
				temperature: params.temperature,
				maxTokens: params.maxTokens,
			});

			const normalizedError = normalizeError(error, 'Genkit API call failed', context);

			errorLogger.error('Genkit API error', normalizedError, context);
			throw normalizedError;
		}
	}

	/**
	 * Convert OpenAI-style messages to a single prompt for Gemini
	 */
	private convertMessagesToPrompt(messages: Array<{ role: string; content: string }>): string {
		let prompt = '';

		for (const message of messages) {
			switch (message.role) {
				case 'system':
					prompt += `System: ${message.content}\n\n`;
					break;
				case 'user':
					prompt += `User: ${message.content}\n\n`;
					break;
				case 'assistant':
					prompt += `Assistant: ${message.content}\n\n`;
					break;
				default:
					prompt += `${message.content}\n\n`;
			}
		}

		return prompt.trim();
	}

	/**
	 * Create a completion with structured output using Zod schema
	 */
	async createStructuredCompletion<T>(
		params: GeminiCompletionParams,
		schema: z.ZodSchema<T>
	): Promise<{ data: T; usage?: GeminiResponse['usage'] }> {
		const ai = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();

		// Select the appropriate model using string identifier
		const modelName = params.model || llmConfig.geminiModel;
		const modelId = `googleai/${modelName}`;

		// Convert OpenAI-style messages to a single prompt
		const prompt = this.convertMessagesToPrompt(params.messages);

		try {
			const result = await ai.generate({
				model: modelId,
				prompt,
				config: {
					temperature: params.temperature ?? 0.7,
					maxOutputTokens: params.maxTokens ?? 1000,
				},
				output: { schema },
			});

			return {
				data: result.output,
				usage: {
					promptTokens: result.usage?.inputTokens || 0,
					completionTokens: result.usage?.outputTokens || 0,
					totalTokens:
						(result.usage?.inputTokens || 0) + (result.usage?.outputTokens || 0),
				},
			};
		} catch (error) {
			const context = createErrorContext('GeminiService', 'createStructuredCompletion', {
				model: modelName,
				temperature: params.temperature,
				maxTokens: params.maxTokens,
			});

			const normalizedError = normalizeError(
				error,
				'Failed to generate structured output from Genkit',
				context
			);

			errorLogger.error('Genkit structured output error', normalizedError, context);
			throw normalizedError;
		}
	}

	/**
	 * Test the Genkit connection
	 */
	async testConnection(): Promise<boolean> {
		try {
			const response = await this.createChatCompletion({
				messages: [{ role: 'user', content: 'Hello, please respond with "OK"' }],
				temperature: 0,
				maxTokens: 10,
			});

			return response.content.toLowerCase().includes('ok');
		} catch (error) {
			const context = createErrorContext('GeminiService', 'testConnection');
			const normalizedError = normalizeError(error, 'Genkit connection test failed', context);
			errorLogger.error('Genkit connection test failed', normalizedError, context);
			return false;
		}
	}
}
