import { UserRepository } from '@/backend/repositories';
import { UserWithDetail } from '@/models/user';
import { Prisma, Provider, Role } from '@prisma/client';

export interface UserService {
	getUserById(userId: string): Promise<UserWithDetail | null>;
	getUserByProviderId(provider: Provider, providerId: string): Promise<UserWithDetail | null>;
	getUserByUsername(username: string): Promise<UserWithDetail | null>;
	createUser(data: {
		provider: Provider;
		provider_id: string;
		email?: string;
		name?: string;
		role?: Role;
	}): Promise<UserWithDetail>;
	createUserWithPassword(data: {
		username: string;
		password_hash: string;
		provider: Provider;
		provider_id: string;
		role?: Role;
	}): Promise<UserWithDetail>;
	createAdminUser(data: {
		username: string;
		password_hash: string;
		provider: Provider;
		provider_id: string;
	}): Promise<UserWithDetail>;
	updateUserRole(userId: string, role: Role): Promise<UserWithDetail>;
	updatePassword(userId: string, passwordHash: string): Promise<UserWithDetail>;
	getAllUsers(limit?: number, offset?: number): Promise<UserWithDetail[]>;
	getUsersCount(): Promise<number>;
	disableUser(userId: string): Promise<UserWithDetail>;
	enableUser(userId: string): Promise<UserWithDetail>;
}

export class UserServiceImpl implements UserService {
	constructor(private readonly getUserRepository: () => UserRepository) {}

	async getUserById(userId: string): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findById(userId);
		return user as UserWithDetail | null;
	}

	async getUserByProviderId(
		provider: Provider,
		providerId: string
	): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findByProviderId(provider, providerId);
		return user as UserWithDetail | null;
	}

	async getUserByUsername(username: string): Promise<UserWithDetail | null> {
		const user = await this.getUserRepository().findByUsername(username);
		return user as UserWithDetail | null;
	}

	async createUser(data: {
		provider: Provider;
		provider_id: string;
		email?: string;
		name?: string;
		role?: Role;
	}): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			role: data.role || Role.USER,
			disabled: false,
		} as Prisma.UserCreateInput);
		return user as UserWithDetail;
	}

	async createUserWithPassword(data: {
		username: string;
		password_hash: string;
		provider: Provider;
		provider_id: string;
		role?: Role;
	}): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			role: data.role || Role.USER,
			disabled: false,
		} as Prisma.UserCreateInput);
		return user as UserWithDetail;
	}

	async createAdminUser(data: {
		username: string;
		password_hash: string;
		provider: Provider;
		provider_id: string;
	}): Promise<UserWithDetail> {
		const user = await this.getUserRepository().create({
			...data,
			role: Role.ADMIN,
			disabled: false,
		} as Prisma.UserCreateInput);
		return user as UserWithDetail;
	}

	async updateUserRole(userId: string, role: Role): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			role,
		} as Prisma.UserUpdateInput);
		return user as UserWithDetail;
	}

	async updatePassword(userId: string, passwordHash: string): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			password_hash: passwordHash,
		} as Prisma.UserUpdateInput);
		return user as UserWithDetail;
	}

	async getAllUsers(limit: number = 50, offset: number = 0): Promise<UserWithDetail[]> {
		const users = await this.getUserRepository().findWithPagination({}, { limit, offset });
		return users as UserWithDetail[];
	}

	async getUsersCount(): Promise<number> {
		return await this.getUserRepository().count();
	}

	async disableUser(userId: string): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			disabled: true,
		} as Prisma.UserUpdateInput);
		return user as UserWithDetail;
	}

	async enableUser(userId: string): Promise<UserWithDetail> {
		const user = await this.getUserRepository().update(userId, {
			disabled: false,
		} as Prisma.UserUpdateInput);
		return user as UserWithDetail;
	}
}
