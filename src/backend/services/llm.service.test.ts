import { LLMService, GrammarPracticeParams, GrammarPracticeResultItem } from './llm.service';
import { Language, Difficulty } from '@prisma/client';
import { mockGrammarPracticeParams, mockGrammarPracticeResult } from '@/test/fixtures';

// Mock OpenAI
jest.mock('openai', () => ({
	OpenAI: jest.fn().mockImplementation(() => ({
		chat: {
			completions: {
				create: jest.fn(),
			},
		},
	})),
}));

describe('LLMService - Grammar Practice', () => {
	let llmService: LLMService;
	let mockOpenAI: any;

	beforeEach(() => {
		// Reset mocks
		jest.clearAllMocks();
		
		// Create service instance
		llmService = new LLMService();
		
		// Get mock OpenAI instance
		const OpenAI = require('openai').OpenAI;
		mockOpenAI = new OpenAI();
	});

	describe('generateGrammarPractice', () => {
		it('should generate grammar practice successfully with valid parameters', async () => {
			// Arrange
			const params = mockGrammarPracticeParams();
			const expectedResult = [mockGrammarPracticeResult()];
			
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [
					{
						message: {
							content: JSON.stringify(expectedResult),
						},
					},
				],
				usage: {
					prompt_tokens: 100,
					completion_tokens: 200,
					total_tokens: 300,
				},
			});

			// Act
			const result = await llmService.generateGrammarPractice(params);

			// Assert
			expect(result).toEqual(expectedResult);
			expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(1);
			expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
				expect.objectContaining({
					model: expect.any(String),
					messages: expect.arrayContaining([
						expect.objectContaining({
							role: 'system',
							content: expect.stringContaining('grammar practice'),
						}),
						expect.objectContaining({
							role: 'user',
							content: expect.stringContaining('technology'),
						}),
					]),
					temperature: expect.any(Number),
					max_tokens: expect.any(Number),
				})
			);
		});

		it('should handle different difficulty levels', async () => {
			// Test BEGINNER difficulty
			const beginnerParams = mockGrammarPracticeParams({ difficulty: Difficulty.BEGINNER });
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: JSON.stringify([mockGrammarPracticeResult()]) } }],
				usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
			});

			await llmService.generateGrammarPractice(beginnerParams);
			
			const beginnerCall = mockOpenAI.chat.completions.create.mock.calls[0][0];
			expect(beginnerCall.messages[0].content).toContain('basic');

			// Test ADVANCED difficulty
			jest.clearAllMocks();
			const advancedParams = mockGrammarPracticeParams({ difficulty: Difficulty.ADVANCED });
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: JSON.stringify([mockGrammarPracticeResult()]) } }],
				usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
			});

			await llmService.generateGrammarPractice(advancedParams);
			
			const advancedCall = mockOpenAI.chat.completions.create.mock.calls[0][0];
			expect(advancedCall.messages[0].content).toContain('complex');
		});

		it('should handle different error densities', async () => {
			const params = mockGrammarPracticeParams({ errorDensity: 'high' });
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: JSON.stringify([mockGrammarPracticeResult()]) } }],
				usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
			});

			await llmService.generateGrammarPractice(params);
			
			const call = mockOpenAI.chat.completions.create.mock.calls[0][0];
			expect(call.messages[0].content).toContain('5-6 errors');
		});

		it('should include sentence count requirement when provided', async () => {
			const params = mockGrammarPracticeParams({ sentenceCount: 10 });
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: JSON.stringify([mockGrammarPracticeResult()]) } }],
				usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
			});

			await llmService.generateGrammarPractice(params);
			
			const call = mockOpenAI.chat.completions.create.mock.calls[0][0];
			expect(call.messages[1].content).toContain('10 sentences');
		});

		it('should handle OpenAI API errors with retries', async () => {
			const params = mockGrammarPracticeParams();
			
			// Mock API to fail twice then succeed
			mockOpenAI.chat.completions.create
				.mockRejectedValueOnce(new Error('API Error 1'))
				.mockRejectedValueOnce(new Error('API Error 2'))
				.mockResolvedValueOnce({
					choices: [{ message: { content: JSON.stringify([mockGrammarPracticeResult()]) } }],
					usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
				});

			const result = await llmService.generateGrammarPractice(params);
			
			expect(result).toHaveLength(1);
			expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(3);
		});

		it('should throw error after max retries', async () => {
			const params = mockGrammarPracticeParams();
			
			// Mock API to always fail
			mockOpenAI.chat.completions.create.mockRejectedValue(new Error('Persistent API Error'));

			await expect(llmService.generateGrammarPractice(params)).rejects.toThrow();
			expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(3); // Initial + 2 retries
		});

		it('should handle invalid JSON response', async () => {
			const params = mockGrammarPracticeParams();
			
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: 'Invalid JSON response' } }],
				usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
			});

			await expect(llmService.generateGrammarPractice(params)).rejects.toThrow();
		});

		it('should validate required parameters', async () => {
			// Test missing keywords
			const invalidParams = { ...mockGrammarPracticeParams(), keywords: [] };
			
			await expect(llmService.generateGrammarPractice(invalidParams)).rejects.toThrow();
		});

		it('should handle different language combinations', async () => {
			const params = mockGrammarPracticeParams({
				language: Language.VI,
				source_language: Language.EN,
				target_language: Language.VI,
			});
			
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: JSON.stringify([mockGrammarPracticeResult()]) } }],
				usage: { prompt_tokens: 50, completion_tokens: 100, total_tokens: 150 },
			});

			const result = await llmService.generateGrammarPractice(params);
			
			expect(result).toHaveLength(1);
			const call = mockOpenAI.chat.completions.create.mock.calls[0][0];
			expect(call.messages[1].content).toContain('Vietnamese');
		});

		it('should generate multiple practice items when count > 1', async () => {
			const params = mockGrammarPracticeParams({ count: 3 });
			const expectedResults = [
				mockGrammarPracticeResult(),
				mockGrammarPracticeResult(),
				mockGrammarPracticeResult(),
			];
			
			mockOpenAI.chat.completions.create.mockResolvedValue({
				choices: [{ message: { content: JSON.stringify(expectedResults) } }],
				usage: { prompt_tokens: 100, completion_tokens: 300, total_tokens: 400 },
			});

			const result = await llmService.generateGrammarPractice(params);
			
			expect(result).toHaveLength(3);
			expect(result).toEqual(expectedResults);
		});
	});
});
