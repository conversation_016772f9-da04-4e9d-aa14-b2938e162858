import { GeminiService } from './gemini.service';
import { z } from 'zod';

// Mock Genkit
jest.mock('genkit', () => ({
	generate: jest.fn(),
}));

jest.mock('@genkit-ai/googleai', () => ({
	gemini15Flash: 'gemini-1.5-flash',
	gemini15Pro: 'gemini-1.5-pro',
	geminiPro: 'gemini-pro',
}));

jest.mock('@/backend/config/genkit.config', () => ({
	initializeGenkit: jest.fn().mockResolvedValue(undefined),
	isGenkitInitialized: jest.fn().mockReturnValue(true),
}));

// Mock config
jest.mock('@/config', () => ({
	getLLMConfig: jest.fn().mockResolvedValue({
		geminiKey: 'test-gemini-key',
		geminiModel: 'gemini-1.5-flash',
		openAIKey: 'test-openai-key',
		openAIModel: 'gpt-4o-mini',
		defaultProvider: 'gemini',
		maxExamples: 8,
		temperature: 0.7,
		maxTokens: 1000,
	}),
}));

describe('GeminiService', () => {
	let geminiService: GeminiService;
	let mockGenerate: jest.MockedFunction<any>;

	beforeEach(async () => {
		jest.clearAllMocks();

		// Get the mocked generate function
		const { generate } = require('genkit');
		mockGenerate = generate as jest.MockedFunction<any>;

		geminiService = new GeminiService();

		// Wait for initialization to complete
		await new Promise((resolve) => setTimeout(resolve, 10));
	});

	describe('createChatCompletion', () => {
		it('should create chat completion successfully', async () => {
			// Arrange
			const mockResponse = {
				text: () => 'Hello, this is a test response',
				usage: {
					inputTokens: 10,
					outputTokens: 8,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			const params = {
				messages: [
					{ role: 'system' as const, content: 'You are a helpful assistant' },
					{ role: 'user' as const, content: 'Hello' },
				],
				temperature: 0.7,
				maxTokens: 100,
			};

			// Act
			const result = await geminiService.createChatCompletion(params);

			// Assert
			expect(result).toEqual({
				content: 'Hello, this is a test response',
				usage: {
					promptTokens: 10,
					completionTokens: 8,
					totalTokens: 18,
				},
			});

			expect(mockGenerate).toHaveBeenCalledWith({
				model: 'gemini-1.5-flash',
				prompt: 'System: You are a helpful assistant\n\nUser: Hello',
				config: {
					temperature: 0.7,
					maxOutputTokens: 100,
				},
			});
		});

		it('should handle JSON response format', async () => {
			// Arrange
			const mockJsonResponse = { message: 'Hello', status: 'success' };
			const mockResponse = {
				text: () => JSON.stringify(mockJsonResponse),
				usage: {
					inputTokens: 10,
					outputTokens: 8,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			const params = {
				messages: [{ role: 'user' as const, content: 'Generate JSON' }],
				responseFormat: { type: 'json_object' },
			};

			// Act
			const result = await geminiService.createChatCompletion(params);

			// Assert
			expect(result.content).toBe(JSON.stringify(mockJsonResponse));
		});

		it('should handle API errors gracefully', async () => {
			// Arrange
			mockGenerate.mockRejectedValue(new Error('API Error'));

			const params = {
				messages: [{ role: 'user' as const, content: 'Hello' }],
			};

			// Act & Assert
			await expect(geminiService.createChatCompletion(params)).rejects.toThrow(
				'Genkit API call failed: API Error'
			);
		});
	});

	describe('createStructuredCompletion', () => {
		it('should create structured completion with Zod schema', async () => {
			// Arrange
			const schema = z.object({
				name: z.string(),
				age: z.number(),
			});

			const mockData = { name: 'John', age: 30 };
			const mockResponse = {
				text: () => JSON.stringify(mockData),
				usage: {
					inputTokens: 15,
					outputTokens: 10,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			const params = {
				messages: [{ role: 'user' as const, content: 'Generate user data' }],
			};

			// Act
			const result = await geminiService.createStructuredCompletion(params, schema);

			// Assert
			expect(result.data).toEqual(mockData);
			expect(result.usage).toEqual({
				promptTokens: 15,
				completionTokens: 10,
				totalTokens: 25,
			});
		});

		it('should handle invalid JSON in structured completion', async () => {
			// Arrange
			const schema = z.object({
				name: z.string(),
				age: z.number(),
			});

			const mockResponse = {
				text: () => 'Invalid JSON response',
				usage: {
					inputTokens: 15,
					outputTokens: 10,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			const params = {
				messages: [{ role: 'user' as const, content: 'Generate user data' }],
			};

			// Act & Assert
			await expect(geminiService.createStructuredCompletion(params, schema)).rejects.toThrow(
				'Invalid response format from Genkit'
			);
		});
	});

	describe('testConnection', () => {
		it('should return true for successful connection test', async () => {
			// Arrange
			const mockResponse = {
				text: () => 'OK',
				usage: {
					inputTokens: 5,
					outputTokens: 1,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act
			const result = await geminiService.testConnection();

			// Assert
			expect(result).toBe(true);
		});

		it('should return false for failed connection test', async () => {
			// Arrange
			mockGenerate.mockRejectedValue(new Error('Connection failed'));

			// Act
			const result = await geminiService.testConnection();

			// Assert
			expect(result).toBe(false);
		});
	});

	describe('convertMessagesToPrompt', () => {
		it('should convert OpenAI-style messages to Gemini prompt format', async () => {
			// This is testing the private method indirectly through createChatCompletion
			const params = {
				messages: [
					{ role: 'system' as const, content: 'You are helpful' },
					{ role: 'user' as const, content: 'Hello' },
					{ role: 'assistant' as const, content: 'Hi there!' },
					{ role: 'user' as const, content: 'How are you?' },
				],
			};

			const mockResponse = {
				text: () => 'I am fine',
				usage: {
					inputTokens: 20,
					outputTokens: 5,
				},
			};

			mockGenerate.mockResolvedValue(mockResponse);

			// Act
			await geminiService.createChatCompletion(params);

			// Assert
			const expectedPrompt =
				'System: You are helpful\n\nUser: Hello\n\nAssistant: Hi there!\n\nUser: How are you?';
			expect(mockGenerate).toHaveBeenCalledWith({
				model: 'gemini-1.5-flash',
				prompt: expectedPrompt,
				config: {
					temperature: 0.7,
					maxOutputTokens: 1000,
				},
			});
		});
	});
});
