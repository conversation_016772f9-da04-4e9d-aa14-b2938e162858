'use client';

import { ErrorBoundary } from '@/components/error/error-boundary';
import { ErrorProvider } from '@/contexts/error-context';
import { apiClient } from '@/lib/api-interceptor';
import { errorLogger } from '@/lib/error-handling';
import { initializeErrorReporting } from '@/lib/error-reporting';

import { ReactNode, useEffect } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorManagementProviderProps {
	children: ReactNode;
	config?: {
		enableErrorReporting?: boolean;

		enableApiInterception?: boolean;
		errorReportingEndpoint?: string;
		errorReportingApiKey?: string;
		environment?: string;
		buildVersion?: string;
		userId?: string;
		sessionId?: string;
	};
}

// ============================================================================
// ERROR MANAGEMENT PROVIDER
// ============================================================================

export function ErrorManagementProvider({ children, config = {} }: ErrorManagementProviderProps) {
	const {
		enableErrorReporting = true,
		enableApiInterception = true,
		errorReportingEndpoint,
		errorReportingApiKey,
		environment = process.env.NODE_ENV || 'development',
		buildVersion = process.env.NEXT_PUBLIC_BUILD_VERSION,
		userId,
		sessionId,
	} = config;

	// Initialize error management systems
	useEffect(() => {
		// Configure error logger
		errorLogger.configure({
			enableConsoleLogging: true,
			enableRemoteLogging: !!errorReportingEndpoint,
			remoteEndpoint: errorReportingEndpoint,
		});

		// Initialize error reporting
		if (enableErrorReporting) {
			try {
				initializeErrorReporting({
					enabled: true,
					endpoint: errorReportingEndpoint,
					apiKey: errorReportingApiKey,
					environment,
					buildVersion,
					userId,
					sessionId: sessionId || generateSessionId(),
					maxReportsPerSession: 50,
					enableLocalStorage: true,
					enableConsoleReporting: environment === 'development',
					onReportSent: (report) => {
						errorLogger.debug(
							'Error report sent successfully',
							{ reportId: report.id },
							'ErrorManagement'
						);
					},
					onReportFailed: (report, error) => {
						errorLogger.error(
							'Failed to send error report',
							error,
							{ reportId: report.id },
							'ErrorManagement'
						);
					},
				});

				errorLogger.info('Error reporting initialized', { environment }, 'ErrorManagement');
			} catch (error) {
				errorLogger.error(
					'Failed to initialize error reporting',
					error instanceof Error ? error : new Error(String(error)),
					{},
					'ErrorManagement'
				);
			}
		}

		// Configure API interceptor
		if (enableApiInterception) {
			// The apiClient is already configured, but we can add global handlers here
			errorLogger.info('API error interception enabled', {}, 'ErrorManagement');
		}

		// Network detection removed

		// Log initialization complete
		errorLogger.info(
			'Error management system initialized',
			{
				enableErrorReporting,
				enableApiInterception,
				environment,
			},
			'ErrorManagement'
		);

		// Development helpers (client-side only)
		if (process.env.NODE_ENV === 'development') {
			(window as any).__errorManagement = {
				errorLogger,
				apiClient,
				triggerTestError: () => {
					throw new Error('Test error for debugging');
				},
				triggerTestAsyncError: async () => {
					await new Promise((resolve) => setTimeout(resolve, 100));
					throw new Error('Test async error for debugging');
				},
			};
		}

		// Cleanup function
		return () => {
			// Network detection cleanup removed
		};
	}, [
		enableErrorReporting,
		enableApiInterception,
		errorReportingEndpoint,
		errorReportingApiKey,
		environment,
		buildVersion,
		userId,
		sessionId,
	]);

	return (
		<ErrorProvider>
			<ErrorBoundary
				level="page"
				name="RootErrorBoundary"
				onError={(error, errorInfo) => {
					errorLogger.error(
						'Root error boundary caught error',
						error,
						{ errorInfo },
						'RootErrorBoundary'
					);
				}}
			>
				{children}
			</ErrorBoundary>
		</ErrorProvider>
	);
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function generateSessionId(): string {
	return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// ============================================================================
// HIGHER-ORDER COMPONENT FOR ERROR BOUNDARIES
// ============================================================================

interface WithErrorBoundaryOptions {
	level?: 'page' | 'section' | 'component';
	name?: string;
	fallback?: (error: any, errorInfo: any, retry: () => void) => ReactNode;
}

export function withErrorBoundary<P extends object>(
	Component: React.ComponentType<P>,
	options: WithErrorBoundaryOptions = {}
) {
	const { level = 'component', name, fallback } = options;

	const WrappedComponent = (props: P) => {
		return (
			<ErrorBoundary
				level={level}
				name={name || Component.displayName || Component.name}
				fallback={fallback}
				onError={(error, errorInfo) => {
					errorLogger.error(
						`Error boundary caught error in ${
							name || Component.displayName || Component.name
						}`,
						error,
						{ errorInfo },
						'ErrorBoundary'
					);
				}}
			>
				<Component {...props} />
			</ErrorBoundary>
		);
	};

	WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

	return WrappedComponent;
}

// ============================================================================
// ERROR MANAGEMENT HOOKS
// ============================================================================

export function useErrorManagement() {
	return {
		errorLogger,
		apiClient,
	};
}
