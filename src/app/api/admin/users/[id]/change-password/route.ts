import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getUserService } from '@/backend/wire';
import { Provider } from '@prisma/client';
import { createErrorContext, errorLogger } from '@/lib/error-handling';

// Validation schema for admin change user password
const changeUserPasswordSchema = z.object({
	newPassword: z.string().min(6, 'New password must be at least 6 characters long'),
});

async function changeUserPasswordHandler(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const userId = params.id;
		
		if (!userId) {
			return NextResponse.json(
				{ success: false, error: 'User ID is required' },
				{ status: 400 }
			);
		}

		// Parse and validate request body
		const body = await request.json();
		const { newPassword } = changeUserPasswordSchema.parse(body);

		// Get user service
		const userService = getUserService();
		
		// Check if user exists
		const user = await userService.getUserById(userId);
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'User not found' },
				{ status: 404 }
			);
		}

		// Check if user uses username/password authentication
		if (user.provider !== Provider.USERNAME_PASSWORD) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Password change not supported for this account type. User uses ' + user.provider + ' authentication.' 
				},
				{ status: 400 }
			);
		}

		// Hash new password
		const hashedNewPassword = await bcrypt.hash(newPassword, 10);

		// Update password in database
		await userService.updatePassword(userId, hashedNewPassword);

		// Log admin action (get admin user ID from request headers set by middleware)
		const adminUserId = request.headers.get('x-user-id');
		
		// TODO: Add audit logging for admin password change
		console.log(`Admin ${adminUserId} changed password for user ${userId}`);

		return NextResponse.json({
			success: true,
			message: 'User password changed successfully',
		});

	} catch (error) {
		// Handle validation errors
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		// Enhanced error logging
		errorLogger.error(
			'Failed to change user password (admin)',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('AdminChangeUserPasswordAPI', 'change_user_password'),
			'AdminChangeUserPasswordAPI'
		);

		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
export const PUT = withAdminAuth(changeUserPasswordHandler);
