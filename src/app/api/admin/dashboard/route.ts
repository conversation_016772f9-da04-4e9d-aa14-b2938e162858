import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';

/**
 * GET /api/admin/dashboard
 * Get admin dashboard statistics and overview data
 */
async function GET(request: NextRequest) {
	try {
		const adminService = getAdminService();

		// Get comprehensive admin stats
		const stats = await adminService.getAdminStats();
		const systemHealth = await adminService.getSystemHealth();
		const recentActivity = await adminService.getRecentActivity(10);

		return NextResponse.json({
			success: true,
			data: {
				stats,
				systemHealth,
				recentActivity,
			},
		});
	} catch (error) {
		console.error('Error fetching admin dashboard data:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch dashboard data',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
export { wrappedGET as GET };
