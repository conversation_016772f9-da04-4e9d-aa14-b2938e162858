import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getCacheService } from '@/backend/cache-init.server';
import { createErrorContext, errorLogger } from '@/lib/error-handling';

async function clearCacheHandler(request: NextRequest) {
	try {
		// Get cache service
		const cacheService = await getCacheService();

		// Clear all caches
		await cacheService.flush();

		// Log admin action (get admin user ID from request headers set by middleware)
		const adminUserId = request.headers.get('x-user-id');
		console.log(`Admin ${adminUserId} cleared all caches`);

		return NextResponse.json({
			success: true,
			message: 'All caches cleared successfully',
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		// Enhanced error logging
		errorLogger.error(
			'Failed to clear cache (admin)',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('AdminClearCacheAPI', 'clear_cache'),
			'AdminClearCacheAPI'
		);

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to clear cache',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
export const POST = withAdminAuth(clearCacheHandler);
