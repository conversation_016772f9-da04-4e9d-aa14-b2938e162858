import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getAuthService } from '@/backend/wire';
import { auth } from '@/lib';
import { createErrorContext, errorLogger } from '@/lib/error-handling';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for change password request
const changePasswordSchema = z.object({
	currentPassword: z.string().min(1, 'Current password is required'),
	newPassword: z.string().min(6, 'New password must be at least 6 characters long'),
});

export async function POST(request: NextRequest) {
	try {
		// Check authentication
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError('Unauthorized');
		}

		// Parse and validate request body
		const body = await request.json();
		const { currentPassword, newPassword } = changePasswordSchema.parse(body);

		// Get client IP and user agent for audit logging
		const ipAddress = request.headers.get('x-forwarded-for') || 
						 request.headers.get('x-real-ip') || 
						 'unknown';
		const userAgent = request.headers.get('user-agent') || 'unknown';

		// Change password using auth service
		const authService = getAuthService();
		await authService.changePassword(
			userId,
			currentPassword,
			newPassword,
			ipAddress,
			userAgent
		);

		return NextResponse.json({
			success: true,
			message: 'Password changed successfully',
		});

	} catch (error) {
		// Handle validation errors
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		// Handle unauthorized errors
		if (error instanceof UnauthorizedError) {
			return NextResponse.json(
				{
					success: false,
					error: error.message,
				},
				{ status: 401 }
			);
		}

		// Handle business logic errors (invalid password, etc.)
		if (error instanceof Error) {
			// Check for specific error messages that should be returned to user
			const userFacingErrors = [
				'User not found',
				'Password change not supported for this account type',
				'Current password is incorrect',
				'New password must be at least 6 characters long',
			];

			if (userFacingErrors.some(msg => error.message.includes(msg))) {
				return NextResponse.json(
					{
						success: false,
						error: error.message,
					},
					{ status: 400 }
				);
			}
		}

		// Enhanced error logging for unexpected errors
		errorLogger.error(
			'Failed to change password',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('ChangePasswordAPI', 'change_password'),
			'ChangePasswordAPI'
		);

		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
			},
			{ status: 500 }
		);
	}
}
