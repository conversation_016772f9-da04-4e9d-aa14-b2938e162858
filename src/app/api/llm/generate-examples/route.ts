import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const generateExamplesSchema = z.object({
	term: z.string().min(1, 'Term is required'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
	existingExamples: z
		.array(
			z.object({
				EN: z.string(),
				VI: z.string(),
			})
		)
		.optional()
		.default([]),
	count: z.number().min(1).max(10).optional().default(3),
});

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for generating examples.');

		const body = await request.json();
		const validatedData = generateExamplesSchema.parse(body);
		const { term, source_language, target_language, existingExamples, count } = validatedData;

		const llmService = await getLLMService();
		const examples = await llmService.generateAdditionalExamples({
			term,
			source_language,
			target_language,
			existingExamples,
			count,
		});

		return NextResponse.json({ examples });
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Generate examples error:', error);
		return NextResponse.json({ error: 'Failed to generate examples' }, { status: 500 });
	}
}
