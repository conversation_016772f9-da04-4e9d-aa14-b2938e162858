import { renderHook, act } from '@testing-library/react';
import { useGrammarPractice } from './use-grammar-practice';
import { mockCollection, mockGrammarPracticeResult } from '@/test/fixtures';
import { Difficulty } from '@prisma/client';

// Mock the contexts
const mockGenerateGrammarPractice = jest.fn();
const mockSelectedKeywords = ['technology', 'innovation'];

jest.mock('@/contexts', () => ({
	useKeywordsContext: () => ({
		selectedKeywords: mockSelectedKeywords,
	}),
	useLLM: () => ({
		generateGrammarPractice: mockGenerateGrammarPractice,
		isLoading: false,
		error: null,
	}),
}));

describe('useGrammarPractice', () => {
	const mockCollectionData = mockCollection();

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should initialize with default state', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		expect(result.current.paragraphs).toEqual([]);
		expect(result.current.selections).toEqual({});
		expect(result.current.generationState).toEqual({
			loading: false,
			error: null,
			success: false,
		});
		expect(result.current.difficulty).toBe(Difficulty.BEGINNER);
		expect(result.current.errorDensity).toBe('medium');
		expect(result.current.selectedKeywords).toEqual(mockSelectedKeywords);
	});

	it('should update difficulty', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		act(() => {
			result.current.setDifficulty(Difficulty.ADVANCED);
		});

		expect(result.current.difficulty).toBe(Difficulty.ADVANCED);
	});

	it('should update error density', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		act(() => {
			result.current.setErrorDensity('high');
		});

		expect(result.current.errorDensity).toBe('high');
	});

	it('should generate paragraphs successfully', async () => {
		const mockResults = [mockGrammarPracticeResult(), mockGrammarPracticeResult()];
		mockGenerateGrammarPractice.mockResolvedValue(mockResults);

		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(mockGenerateGrammarPractice).toHaveBeenCalledWith({
			keywords: mockSelectedKeywords,
			language: mockCollectionData.target_language,
			source_language: mockCollectionData.source_language,
			target_language: mockCollectionData.target_language,
			difficulty: Difficulty.BEGINNER,
			count: 5, // PARAGRAPH_COUNT
			errorDensity: 'medium',
		});

		expect(result.current.paragraphs).toEqual(mockResults);
		expect(result.current.generationState.success).toBe(true);
		expect(result.current.generationState.loading).toBe(false);
		expect(result.current.generationState.error).toBe(null);
	});

	it('should handle generation errors', async () => {
		const errorMessage = 'Failed to generate paragraphs';
		mockGenerateGrammarPractice.mockRejectedValue(new Error(errorMessage));

		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(result.current.generationState.loading).toBe(false);
		expect(result.current.generationState.success).toBe(false);
		expect(result.current.generationState.error).toEqual(new Error(errorMessage));
		expect(result.current.paragraphs).toEqual([]);
	});

	it('should handle missing keywords error', async () => {
		// Create a new mock for this test
		const mockUseKeywordsContext = jest.fn().mockReturnValue({
			selectedKeywords: [],
		});

		// Temporarily replace the mock
		const originalMock = require('@/contexts').useKeywordsContext;
		require('@/contexts').useKeywordsContext = mockUseKeywordsContext;

		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(result.current.generationState.error).toEqual(new Error('No keywords selected'));
		expect(mockGenerateGrammarPractice).not.toHaveBeenCalled();

		// Restore original mock
		require('@/contexts').useKeywordsContext = originalMock;
	});

	it('should handle missing collection error', async () => {
		const { result } = renderHook(() => useGrammarPractice(null));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(result.current.generationState.error).toEqual(new Error('Collection not found'));
		expect(mockGenerateGrammarPractice).not.toHaveBeenCalled();
	});

	it('should handle empty generation result', async () => {
		mockGenerateGrammarPractice.mockResolvedValue([]);

		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(result.current.generationState.error).toEqual(new Error('No paragraphs generated'));
		expect(result.current.paragraphs).toEqual([]);
	});

	it('should toggle word selection', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		const paragraphIndex = 0;
		const word = 'test';
		const wordIndex = 5;

		act(() => {
			result.current.toggleWordSelection(paragraphIndex, word, wordIndex);
		});

		expect(result.current.selections[paragraphIndex]).toEqual([{ word, index: wordIndex }]);

		// Toggle again to remove
		act(() => {
			result.current.toggleWordSelection(paragraphIndex, word, wordIndex);
		});

		expect(result.current.selections[paragraphIndex]).toEqual([]);
	});

	it('should handle multiple word selections for same paragraph', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		const paragraphIndex = 0;
		const word1 = 'test1';
		const wordIndex1 = 5;
		const word2 = 'test2';
		const wordIndex2 = 10;

		act(() => {
			result.current.toggleWordSelection(paragraphIndex, word1, wordIndex1);
			result.current.toggleWordSelection(paragraphIndex, word2, wordIndex2);
		});

		expect(result.current.selections[paragraphIndex]).toEqual([
			{ word: word1, index: wordIndex1 },
			{ word: word2, index: wordIndex2 },
		]);
	});

	it('should handle word selections for different paragraphs', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		const word1 = 'test1';
		const wordIndex1 = 5;
		const word2 = 'test2';
		const wordIndex2 = 10;

		act(() => {
			result.current.toggleWordSelection(0, word1, wordIndex1);
			result.current.toggleWordSelection(1, word2, wordIndex2);
		});

		expect(result.current.selections[0]).toEqual([{ word: word1, index: wordIndex1 }]);
		expect(result.current.selections[1]).toEqual([{ word: word2, index: wordIndex2 }]);
	});

	it('should reset state when generating new paragraphs', async () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		// Set some initial state
		act(() => {
			result.current.toggleWordSelection(0, 'test', 5);
		});

		const mockResults = [mockGrammarPracticeResult()];
		mockGenerateGrammarPractice.mockResolvedValue(mockResults);

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(result.current.selections).toEqual({});
		expect(result.current.paragraphs).toEqual(mockResults);
	});

	it('should use default language values when collection languages are null', async () => {
		const collectionWithoutLanguages = {
			...mockCollectionData,
			target_language: null,
			source_language: null,
		};

		mockGenerateGrammarPractice.mockResolvedValue([mockGrammarPracticeResult()]);

		const { result } = renderHook(() => useGrammarPractice(collectionWithoutLanguages));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(mockGenerateGrammarPractice).toHaveBeenCalledWith(
			expect.objectContaining({
				language: 'EN',
				source_language: 'VI',
				target_language: 'EN',
			})
		);
	});

	it('should provide loading state from generation', () => {
		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.hasError).toBe(false);
		expect(result.current.errorMessage).toBeUndefined();
	});

	it('should provide error state information', async () => {
		const errorMessage = 'Test error';
		mockGenerateGrammarPractice.mockRejectedValue(new Error(errorMessage));

		const { result } = renderHook(() => useGrammarPractice(mockCollectionData));

		await act(async () => {
			await result.current.generateParagraphs();
		});

		expect(result.current.hasError).toBe(true);
		expect(result.current.errorMessage).toBe(errorMessage);
	});
});
