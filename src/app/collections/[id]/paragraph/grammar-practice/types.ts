// Types for Grammar Practice functionality
export type SelectedWord = {
	word: string;
	index: number;
};

export type ErrorDensity = 'low' | 'medium' | 'high';

// Enhanced types for new grammar structure
export type WordToken = {
	text: string;
	position: number;
	isError: boolean;
	errorType: string | null;
	correctedText: string | null;
	explanation: {
		source_language: string;
		target_language: string;
	} | null;
};

export type EnhancedError = {
	errorText: string;
	correctedText: string;
	errorType: string;
	startPosition: number;
	endPosition: number;
	explanation: {
		source_language: string;
		target_language: string;
	};
};

export type GrammarSummary = {
	totalWords: number;
	totalErrors: number;
	errorTypes: string[];
};

export type GenerationState = {
	loading: boolean;
	error: Error | null;
	success: boolean;
};

export type Progress = {
	current: number;
	total: number;
	correct: number;
};

// Error classification types
export type ErrorDifficulty = 'easy' | 'medium' | 'hard' | 'very_hard';

export type ErrorType = {
	id: string;
	name: string;
	example: string;
	difficulty: ErrorDifficulty;
	description: string;
	category: 'grammar' | 'vocabulary' | 'mechanics' | 'style';
};

export type ErrorCategory = {
	id: string;
	name: string;
	description: string;
	color: string;
	icon: string;
	errors: readonly ErrorType[];
};
