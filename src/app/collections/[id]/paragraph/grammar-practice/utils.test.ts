import { getErrorColor } from './utils';
import { ERROR_TYPE_COLORS } from './constants';

describe('Grammar Practice Utils', () => {
	describe('getErrorColor', () => {
		it('should return correct color for grammar error type', () => {
			expect(getErrorColor('grammar')).toBe(ERROR_TYPE_COLORS.grammar);
			expect(getErrorColor('Grammar')).toBe(ERROR_TYPE_COLORS.grammar);
			expect(getErrorColor('GRAMMAR')).toBe(ERROR_TYPE_COLORS.grammar);
		});

		it('should return correct color for vocabulary error type', () => {
			expect(getErrorColor('vocabulary')).toBe(ERROR_TYPE_COLORS.vocabulary);
			expect(getErrorColor('Vocabulary')).toBe(ERROR_TYPE_COLORS.vocabulary);
			expect(getErrorColor('VOCABULARY')).toBe(ERROR_TYPE_COLORS.vocabulary);
		});

		it('should return correct color for spelling error type', () => {
			expect(getErrorColor('spelling')).toBe(ERROR_TYPE_COLORS.spelling);
			expect(getErrorColor('Spelling')).toBe(ERROR_TYPE_COLORS.spelling);
			expect(getErrorColor('SPELLING')).toBe(ERROR_TYPE_COLORS.spelling);
		});

		it('should return correct color for mechanics error type', () => {
			expect(getErrorColor('mechanics')).toBe(ERROR_TYPE_COLORS.mechanics);
			expect(getErrorColor('Mechanics')).toBe(ERROR_TYPE_COLORS.mechanics);
			expect(getErrorColor('MECHANICS')).toBe(ERROR_TYPE_COLORS.mechanics);
		});

		it('should return default color for unknown error types', () => {
			expect(getErrorColor('unknown')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('invalid')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('random-type')).toBe(ERROR_TYPE_COLORS.default);
		});

		it('should handle null and undefined inputs', () => {
			expect(getErrorColor(null as any)).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor(undefined as any)).toBe(ERROR_TYPE_COLORS.default);
		});

		it('should handle special characters and numbers', () => {
			expect(getErrorColor('grammar-123')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('vocabulary!')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('123')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('!@#$')).toBe(ERROR_TYPE_COLORS.default);
		});

		it('should handle whitespace', () => {
			expect(getErrorColor(' grammar ')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('grammar ')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor(' grammar')).toBe(ERROR_TYPE_COLORS.default);
			expect(getErrorColor('   ')).toBe(ERROR_TYPE_COLORS.default);
		});
	});
});
