import { GrammarPracticeResultItem } from '@/backend/services';
import { Button, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { cn } from '@/lib';
import { Language } from '@prisma/client';
import { Eye, EyeOff, Target, AlertCircle, CheckCircle } from 'lucide-react';
import { useState } from 'react';
import type { SelectedWord, WordToken } from './types';

export const EnhancedGrammarPracticeItem = ({
	item,
	index,
	selectedWords,
	onWordSelection,
	targetLanguage,
	sourceLanguage,
}: {
	item: GrammarPracticeResultItem;
	index: number;
	selectedWords: SelectedWord[];
	onWordSelection: (paragraphIndex: number, word: string, wordIndex: number) => void;
	targetLanguage: Language;
	sourceLanguage: Language;
}) => {
	const [showResults, setShowResults] = useState(false);

	// Use enhanced structure if available, fallback to old method
	const useEnhancedStructure = item.wordTokens && item.wordTokens.length > 0;
	const words = useEnhancedStructure
		? item.wordTokens
		: (item.paragraphWithErrors.split(/\s+/).map((text, position) => ({
				text,
				position,
				isError: false,
		  })) as WordToken[]);

	// Get error explanation for a word token
	const getErrorExplanation = (token: WordToken) => {
		if (token.explanation) return token.explanation;

		// Fallback: find from allErrors
		return item.allErrors.find((error) =>
			error.errorText.toLowerCase().includes(token.text.toLowerCase())
		)?.explanation;
	};

	// Render word token with enhanced styling
	const renderWordToken = (token: WordToken, tokenIndex: number) => {
		const isSelected = selectedWords.some((s) => s.index === tokenIndex);
		const explanation = getErrorExplanation(token);

		return (
			<span key={`${token.text}-${tokenIndex}`} className="relative inline-block">
				<span
					className={cn(
						'relative inline-block cursor-pointer transition-all duration-200 ease-in-out transform rounded-md px-1.5 py-0.5 mt-1 group',
						{
							// Selected state
							'bg-gradient-to-r from-amber-100 to-yellow-100 dark:from-amber-900/50 dark:to-yellow-900/50 outline outline-amber-300 dark:outline-amber-600 shadow-sm':
								isSelected,
							// Error states
							'bg-gradient-to-r from-red-100 to-red-200 dark:from-red-900/50 dark:to-red-800/50 outline outline-red-300 dark:outline-red-600':
								token.isError && showResults,
							// Normal hover state
							'hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-950/30 dark:hover:to-indigo-950/30 hover:outline hover:outline-blue-200 dark:hover:outline-blue-700':
								!isSelected && !token.isError,
						}
					)}
					onClick={() => onWordSelection(index, token.text, tokenIndex)}
				>
					{/* Display word with error indication */}
					{token.isError && showResults ? (
						<>
							{token.correctedText === '' || token.correctedText === null ? (
								// Extra word (should be deleted)
								<>
									<del className="text-red-600 dark:text-red-400">
										{token.text}
									</del>
									<span className="ml-1 text-xs text-red-500">(xóa)</span>
								</>
							) : (
								// Word replacement or correction
								<>
									<del className="text-red-600 dark:text-red-400">
										{token.text}
									</del>
									<span className="mx-1 text-gray-500">→</span>
									<span className="text-green-600 dark:text-green-400">
										{token.correctedText}
									</span>
								</>
							)}
						</>
					) : (
						<span className={token.isError && !showResults ? 'relative' : ''}>
							{token.text}
						</span>
					)}

					{/* Error explanation tooltip */}
					{token.isError && explanation && showResults && (
						<span
							className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-white dark:bg-background text-primary dark:text-primary text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-[320px] break-words border border-border dark:border-border"
							style={{
								left: 'clamp(0px, 50%, calc(100vw - 320px))',
							}}
						>
							<span className="inline-flex items-center gap-1 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
								{token.errorType}
							</span>
							<div className="my-2">
								<div className="font-medium mb-1 text-primary dark:text-primary">
									<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
									:
								</div>
								<div className="text-xs text-primary/80 dark:text-primary/80">
									{explanation.target_language}
								</div>
							</div>
							<div>
								<div className="font-medium mb-1 text-primary dark:text-primary">
									<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />
									:
								</div>
								<div className="text-xs text-primary/80 dark:text-primary/80">
									{explanation.source_language}
								</div>
							</div>
						</span>
					)}
				</span>
				{tokenIndex < words.length - 1 && ' '}
			</span>
		);
	};

	// Render missing word indicators (for future enhancement)
	const renderMissingWordIndicators = () => {
		if (!useEnhancedStructure) return null;

		// Find missing word errors
		const missingWordErrors = item.allErrors.filter(
			(error) => error.errorText === '' && error.correctedText !== ''
		);

		return missingWordErrors.map((error, index) => (
			<span key={`missing-${index}`} className="relative inline-block">
				<span className="inline-flex items-center gap-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 px-2 py-1 rounded-md text-xs border border-green-300 dark:border-green-600">
					<span className="text-green-600">+</span>
					{error.correctedText}
					<span className="text-xs">(thêm)</span>
				</span>
			</span>
		));
	};

	return (
		<div className="bg-white dark:bg-background/50 border-2 border-border dark:border-border rounded-xl p-6 space-y-6 shadow-sm hover:shadow-md transition-all duration-200">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 text-blue-700 dark:text-blue-300 rounded-full border-2 border-blue-200 dark:border-blue-700 font-bold text-sm">
						{index + 1}
					</div>
					<div className="flex items-center gap-2">
						<span className="inline-flex items-center gap-1 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
							<Target className="w-3 h-3" />
							<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
						</span>
						<span className="text-xs text-muted-foreground dark:text-muted-foreground">
							{selectedWords.length} selected
						</span>
						{/* Enhanced structure indicator */}
						{useEnhancedStructure && (
							<span className="inline-flex items-center gap-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs px-2 py-1 rounded-full">
								<CheckCircle className="w-3 h-3" />
								Enhanced
							</span>
						)}
					</div>
				</div>
				<Button
					variant={showResults ? 'default' : 'outline'}
					size="sm"
					onClick={() => setShowResults(!showResults)}
					className="flex items-center gap-2 transition-all duration-200"
				>
					{showResults ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
					<Translate
						text={showResults ? 'grammar.hide_results' : 'grammar.show_results'}
					/>
				</Button>
			</div>

			{/* Content */}
			<div className="space-y-4">
				{/* Practice paragraph */}
				<div className="bg-background/50 dark:bg-background/30 border border-border dark:border-border rounded-lg p-4">
					<div className="text-primary dark:text-primary leading-relaxed text-base">
						{words.map((token, tokenIndex) => renderWordToken(token, tokenIndex))}
						{showResults && renderMissingWordIndicators()}
					</div>
				</div>

				{/* Summary statistics (if available) */}
				{useEnhancedStructure && item.summary && showResults && (
					<div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
						<div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 text-sm">
							<AlertCircle className="w-4 h-4" />
							<span className="font-medium">Thống kê:</span>
							<span>{item.summary.totalWords} từ</span>
							<span>•</span>
							<span>{item.summary.totalErrors} lỗi</span>
							<span>•</span>
							<span>{item.summary.errorTypes.join(', ')}</span>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};
