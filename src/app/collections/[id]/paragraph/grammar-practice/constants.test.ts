import {
	PARAGRAPH_COUNT,
	ERROR_TYPE_ICONS,
	ERROR_CATEGORIES,
	getErrorTypesForDifficulty,
	getErrorDensityDescription,
} from './constants';

describe('Grammar Practice Constants', () => {
	describe('PARAGRAPH_COUNT', () => {
		it('should be a positive number', () => {
			expect(PARAGRAPH_COUNT).toBe(5);
			expect(typeof PARAGRAPH_COUNT).toBe('number');
			expect(PARAGRAPH_COUNT).toBeGreaterThan(0);
		});
	});

	describe('ERROR_TYPE_ICONS', () => {
		it('should contain all required icon mappings', () => {
			expect(ERROR_TYPE_ICONS).toHaveProperty('grammar');
			expect(ERROR_TYPE_ICONS).toHaveProperty('vocabulary');
			expect(ERROR_TYPE_ICONS).toHaveProperty('spelling');
			expect(ERROR_TYPE_ICONS).toHaveProperty('mechanics');
			expect(ERROR_TYPE_ICONS).toHaveProperty('default');
		});

		it('should have string values for all icons', () => {
			Object.values(ERROR_TYPE_ICONS).forEach((icon) => {
				expect(typeof icon).toBe('string');
				expect(icon.length).toBeGreaterThan(0);
			});
		});
	});

	describe('ERROR_CATEGORIES', () => {
		it('should be an array with categories', () => {
			expect(Array.isArray(ERROR_CATEGORIES)).toBe(true);
			expect(ERROR_CATEGORIES.length).toBeGreaterThan(0);
		});

		it('should have valid category structure', () => {
			ERROR_CATEGORIES.forEach((category) => {
				expect(category).toHaveProperty('id');
				expect(category).toHaveProperty('name');
				expect(category).toHaveProperty('description');
				expect(category).toHaveProperty('color');
				expect(category).toHaveProperty('icon');
				expect(category).toHaveProperty('errors');

				expect(typeof category.id).toBe('string');
				expect(typeof category.name).toBe('string');
				expect(typeof category.description).toBe('string');
				expect(typeof category.color).toBe('string');
				expect(typeof category.icon).toBe('string');
				expect(Array.isArray(category.errors)).toBe(true);
			});
		});

		it('should have valid error structure within categories', () => {
			ERROR_CATEGORIES.forEach((category) => {
				category.errors.forEach((error) => {
					expect(error).toHaveProperty('id');
					expect(error).toHaveProperty('name');
					expect(error).toHaveProperty('example');
					expect(error).toHaveProperty('difficulty');
					expect(error).toHaveProperty('description');
					expect(error).toHaveProperty('category');

					expect(typeof error.id).toBe('string');
					expect(typeof error.name).toBe('string');
					expect(typeof error.example).toBe('string');
					expect(['easy', 'medium', 'hard', 'very_hard']).toContain(error.difficulty);
					expect(typeof error.description).toBe('string');
					expect(['grammar', 'vocabulary', 'mechanics', 'style']).toContain(
						error.category
					);
				});
			});
		});
	});

	describe('getErrorTypesForDifficulty', () => {
		it('should return array of strings for BEGINNER difficulty', () => {
			const result = getErrorTypesForDifficulty('BEGINNER');
			expect(Array.isArray(result)).toBe(true);
			expect(result.length).toBeGreaterThan(0);
			result.forEach((errorType) => {
				expect(typeof errorType).toBe('string');
			});
		});

		it('should return array of strings for INTERMEDIATE difficulty', () => {
			const result = getErrorTypesForDifficulty('INTERMEDIATE');
			expect(Array.isArray(result)).toBe(true);
			expect(result.length).toBeGreaterThan(0);
			result.forEach((errorType) => {
				expect(typeof errorType).toBe('string');
			});
		});

		it('should return array of strings for ADVANCED difficulty', () => {
			const result = getErrorTypesForDifficulty('ADVANCED');
			expect(Array.isArray(result)).toBe(true);
			expect(result.length).toBeGreaterThan(0);
			result.forEach((errorType) => {
				expect(typeof errorType).toBe('string');
			});
		});

		it('should return different error types for different difficulties', () => {
			const beginner = getErrorTypesForDifficulty('BEGINNER');
			const intermediate = getErrorTypesForDifficulty('INTERMEDIATE');
			const advanced = getErrorTypesForDifficulty('ADVANCED');

			// Beginner should have basic errors
			expect(beginner).toContain('spelling errors');
			expect(beginner).toContain('capitalization');
			expect(beginner).toContain('basic verb tense');

			// Intermediate should have more complex errors
			expect(intermediate).toContain('preposition usage');
			expect(intermediate).toContain('conjunction errors');

			// Advanced should have the most complex errors
			expect(advanced).toContain('reported speech');
			expect(advanced).toContain('conditional sentences');
		});

		it('should handle invalid difficulty gracefully', () => {
			// Should not throw error for invalid input
			expect(() => getErrorTypesForDifficulty('INVALID' as any)).not.toThrow();
		});
	});

	describe('getErrorDensityDescription', () => {
		it('should return string descriptions for all valid combinations', () => {
			const densities = ['low', 'medium', 'high'] as const;
			const difficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'] as const;

			densities.forEach((density) => {
				difficulties.forEach((difficulty) => {
					const result = getErrorDensityDescription(density, difficulty);
					expect(typeof result).toBe('string');
					expect(result.length).toBeGreaterThan(0);
				});
			});
		});

		it('should include error count information', () => {
			const lowDensity = getErrorDensityDescription('low', 'BEGINNER');
			const mediumDensity = getErrorDensityDescription('medium', 'BEGINNER');
			const highDensity = getErrorDensityDescription('high', 'BEGINNER');

			expect(lowDensity).toContain('1-2 errors');
			expect(mediumDensity).toContain('3-4 errors');
			expect(highDensity).toContain('5-6 errors');
		});

		it('should include difficulty-specific notes', () => {
			const beginnerDesc = getErrorDensityDescription('medium', 'BEGINNER');
			const intermediateDesc = getErrorDensityDescription('medium', 'INTERMEDIATE');
			const advancedDesc = getErrorDensityDescription('medium', 'ADVANCED');

			expect(beginnerDesc).toContain('basic');
			expect(intermediateDesc).toContain('intermediate');
			expect(advancedDesc).toContain('complex');
		});

		it('should handle edge cases', () => {
			// Should not throw for invalid inputs
			expect(() => getErrorDensityDescription('invalid' as any, 'BEGINNER')).not.toThrow();
			expect(() => getErrorDensityDescription('low', 'INVALID' as any)).not.toThrow();
		});
	});
});
