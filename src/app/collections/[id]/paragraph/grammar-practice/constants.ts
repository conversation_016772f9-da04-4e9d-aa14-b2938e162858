// Constants for Grammar Practice
export const PARAGRAPH_COUNT = 5;

export const ERROR_TYPE_ICONS = {
	grammar: 'Settings',
	vocabulary: 'Target',
	spelling: 'Sparkles',
	mechanics: 'RefreshCw',
	default: 'AlertCircle',
} as const;

// Error classification data
export const ERROR_CATEGORIES = [
	{
		id: 'easy',
		name: '<PERSON><PERSON> nhận biết',
		description: 'Người học phổ thông cũng dễ thấy được',
		color: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
		icon: '🟢',
		errors: [
			{
				id: 'spelling',
				name: '<PERSON><PERSON><PERSON> tả (Spelling errors)',
				example: 'Recieve ❌ → Receive ✅',
				difficulty: 'easy' as const,
				description: 'Lỗi viết sai chính tả từ',
				category: 'mechanics' as const,
			},
			{
				id: 'capitalization',
				name: 'Viết hoa sai (Capitalization)',
				example: 'i am a student ❌ → I am a student ✅',
				difficulty: 'easy' as const,
				description: 'Lỗi viết hoa đầu câu, tên riêng',
				category: 'mechanics' as const,
			},
			{
				id: 'verb_tense',
				name: '<PERSON><PERSON> <PERSON><PERSON>i (Verb tense)',
				example: 'She go to school. ❌',
				difficulty: 'easy' as const,
				description: 'Lỗi chia thì động từ không đúng',
				category: 'grammar' as const,
			},
			{
				id: 'sv_agreement',
				name: 'S-V Agreement',
				example: 'He say hello. ❌ → He says hello. ✅',
				difficulty: 'easy' as const,
				description: 'Lỗi chủ ngữ và động từ không hòa hợp',
				category: 'grammar' as const,
			},
			{
				id: 'articles',
				name: 'Sai mạo từ (a/an/the)',
				example: 'a apple ❌ → an apple ✅',
				difficulty: 'easy' as const,
				description: 'Lỗi sử dụng mạo từ không đúng',
				category: 'grammar' as const,
			},
			{
				id: 'basic_punctuation',
				name: 'Dấu câu cơ bản (Punctuation)',
				example: 'How are you. ❌ → How are you? ✅',
				difficulty: 'easy' as const,
				description: 'Lỗi dấu câu cơ bản',
				category: 'mechanics' as const,
			},
		],
	},
	{
		id: 'medium',
		name: 'Trung bình',
		description: 'Nhận biết được nếu có học ngữ pháp bài bản',
		color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',
		icon: '🟡',
		errors: [
			{
				id: 'preposition',
				name: 'Giới từ (Preposition)',
				example: 'good in English ❌ → good at English ✅',
				difficulty: 'medium' as const,
				description: 'Lỗi sử dụng giới từ không phù hợp',
				category: 'grammar' as const,
			},
			{
				id: 'conjunction',
				name: 'Sai liên từ (Conjunction)',
				example: 'Although... but... ❌',
				difficulty: 'medium' as const,
				description: 'Lỗi sử dụng liên từ không đúng',
				category: 'grammar' as const,
			},
			{
				id: 'fragment',
				name: 'Câu thiếu thành phần (Fragment)',
				example: 'Because I was tired. ❌',
				difficulty: 'medium' as const,
				description: 'Câu không hoàn chỉnh, thiếu thành phần',
				category: 'grammar' as const,
			},
			{
				id: 'run_on',
				name: 'Câu quá dài (Run-on sentence)',
				example: 'He ran he fell. ❌',
				difficulty: 'medium' as const,
				description: 'Câu ghép không đúng cách',
				category: 'grammar' as const,
			},
			{
				id: 'passive_voice',
				name: 'Sai thể bị động (Passive)',
				example: 'The cake ate by me. ❌',
				difficulty: 'medium' as const,
				description: 'Lỗi cấu trúc câu bị động',
				category: 'grammar' as const,
			},
			{
				id: 'word_form',
				name: 'Dạng từ sai (Word form)',
				example: 'He was boring. ❌ → He was bored. ✅',
				difficulty: 'medium' as const,
				description: 'Lỗi sử dụng dạng từ không phù hợp',
				category: 'vocabulary' as const,
			},
		],
	},
	{
		id: 'hard',
		name: 'Khó hơn',
		description: 'Cần hiểu ý sâu hoặc ngữ cảnh rõ ràng',
		color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-300',
		icon: '🟠',
		errors: [
			{
				id: 'reported_speech',
				name: 'Câu gián tiếp sai (Reported speech)',
				example: 'She said she is happy. ❌',
				difficulty: 'hard' as const,
				description: 'Lỗi cấu trúc câu gián tiếp',
				category: 'grammar' as const,
			},
			{
				id: 'conditional',
				name: 'Sai câu điều kiện',
				example: 'If I will go... ❌',
				difficulty: 'hard' as const,
				description: 'Lỗi cấu trúc câu điều kiện',
				category: 'grammar' as const,
			},
			{
				id: 'register',
				name: 'Sử dụng từ sai cấp độ (register)',
				example: 'Yo bro trong thư xin việc ❌',
				difficulty: 'hard' as const,
				description: 'Lỗi sử dụng từ không phù hợp với ngữ cảnh trang trọng',
				category: 'style' as const,
			},
			{
				id: 'word_misuse',
				name: 'Lỗi ngữ nghĩa (Word misuse)',
				example: 'He committed a mistake. ❌',
				difficulty: 'hard' as const,
				description: 'Lỗi sử dụng từ sai nghĩa',
				category: 'vocabulary' as const,
			},
			{
				id: 'ambiguity',
				name: 'Câu mơ hồ (Ambiguity)',
				example: 'She told her friend that she loved her. ❓',
				difficulty: 'hard' as const,
				description: 'Câu có thể hiểu theo nhiều cách',
				category: 'style' as const,
			},
			{
				id: 'word_order',
				name: 'Sai trật tự từ (Word order)',
				example: 'Beautiful is she. ❌',
				difficulty: 'hard' as const,
				description: 'Lỗi sắp xếp từ trong câu',
				category: 'grammar' as const,
			},
		],
	},
	{
		id: 'very_hard',
		name: 'Rất khó',
		description: 'Cần khả năng phân tích ngôn ngữ hoặc kinh nghiệm nâng cao',
		color: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300',
		icon: '🔴',
		errors: [
			{
				id: 'coherence',
				name: 'Lỗi liên kết, mạch lạc (coherence)',
				example: 'Ý các câu không liên quan chặt chẽ',
				difficulty: 'very_hard' as const,
				description: 'Lỗi về tính liên kết và mạch lạc của đoạn văn',
				category: 'style' as const,
			},
			{
				id: 'contextual_word',
				name: 'Lỗi dùng từ không phù hợp ngữ cảnh',
				example: 'sympathy thay vì sympathetic',
				difficulty: 'very_hard' as const,
				description: 'Lỗi chọn từ không phù hợp với ngữ cảnh',
				category: 'vocabulary' as const,
			},
			{
				id: 'tone_mismatch',
				name: 'Lỗi giọng điệu (tone mismatch)',
				example: 'Dùng giọng đùa cợt trong bài viết học thuật',
				difficulty: 'very_hard' as const,
				description: 'Lỗi về giọng điệu không phù hợp',
				category: 'style' as const,
			},
			{
				id: 'logical_error',
				name: 'Lỗi logic (Logical error)',
				example: 'I studied hard, so I failed the exam. ❌',
				difficulty: 'very_hard' as const,
				description: 'Lỗi logic trong mối quan hệ nguyên nhân - kết quả',
				category: 'style' as const,
			},
			{
				id: 'homophones',
				name: 'Từ đồng âm khác nghĩa (homophones)',
				example: "There/their/they're hoặc your/you're",
				difficulty: 'very_hard' as const,
				description: 'Lỗi nhầm lẫn từ đồng âm',
				category: 'vocabulary' as const,
			},
			{
				id: 'contextual_ambiguity',
				name: 'Lỗi mơ hồ ngữ cảnh (Contextual ambiguity)',
				example: 'Câu không sai ngữ pháp nhưng khiến người đọc hiểu nhầm',
				difficulty: 'very_hard' as const,
				description: 'Lỗi gây hiểu nhầm do ngữ cảnh',
				category: 'style' as const,
			},
		],
	},
] as const;

export const ERROR_TYPE_COLORS = {
	grammar:
		'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700',
	vocabulary:
		'bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700',
	spelling:
		'bg-pink-100 dark:bg-pink-900/50 text-pink-700 dark:text-pink-300 border-pink-200 dark:border-pink-700',
	mechanics:
		'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-700',
	default:
		'bg-background dark:bg-background/50 text-primary dark:text-primary border-border dark:border-border',
} as const;

// Helper function to get error types based on difficulty
export function getErrorTypesForDifficulty(
	difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
): string[] {
	switch (difficulty) {
		case 'BEGINNER':
			// Focus on easy to recognize errors
			return [
				'spelling errors',
				'capitalization',
				'basic verb tense',
				'subject-verb agreement',
				'basic articles (a/an/the)',
				'basic punctuation',
			];
		case 'INTERMEDIATE':
			// Include medium difficulty errors
			return [
				'preposition usage',
				'conjunction errors',
				'sentence fragments',
				'run-on sentences',
				'passive voice',
				'word form errors',
				'verb tense consistency',
				'article usage',
			];
		case 'ADVANCED':
			// Include harder errors
			return [
				'reported speech',
				'conditional sentences',
				'register appropriateness',
				'word choice and semantics',
				'sentence ambiguity',
				'word order',
				'coherence and cohesion',
				'contextual word usage',
				'tone consistency',
				'logical relationships',
			];
		default:
			return getErrorTypesForDifficulty('INTERMEDIATE');
	}
}

// Helper function to get error density description
export function getErrorDensityDescription(
	density: 'low' | 'medium' | 'high',
	difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
): string {
	const baseDescription = {
		low: '1-2 errors per paragraph',
		medium: '3-4 errors per paragraph',
		high: '5-6 errors per paragraph',
	}[density];

	const difficultyNote = {
		BEGINNER: 'Focus on basic, easily recognizable errors',
		INTERMEDIATE: 'Mix of basic and intermediate-level errors',
		ADVANCED: 'Include complex and subtle errors',
	}[difficulty];

	return `${baseDescription}. ${difficultyNote}.`;
}

// New function to get detailed error requirements with specific distributions
export function getDetailedErrorRequirements(
	density: 'low' | 'medium' | 'high',
	difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
): {
	totalErrors: number;
	mandatoryErrorTypes: Array<{
		type: string;
		minCount: number;
		examples: string[];
	}>;
	description: string;
} {
	switch (difficulty) {
		case 'BEGINNER': {
			const beginnerTotal = density === 'low' ? 2 : density === 'medium' ? 3 : 4;
			return {
				totalErrors: beginnerTotal,
				mandatoryErrorTypes: [
					{
						type: 'spelling errors',
						minCount: 1,
						examples: [
							'recieve → receive',
							'seperate → separate',
							'occured → occurred',
							'definately → definitely',
						],
					},
					{
						type: 'subject-verb agreement',
						minCount: 1,
						examples: [
							'He like music → He likes music',
							'She have a car → She has a car',
							'They was happy → They were happy',
						],
					},
					{
						type: 'basic articles (a/an/the)',
						minCount: density === 'high' ? 1 : 0,
						examples: [
							'a apple → an apple',
							'I need book → I need a book',
							'He is teacher → He is a teacher',
						],
					},
				],
				description: `Include exactly ${beginnerTotal} basic errors that are easily recognizable by most learners.`,
			};
		}

		case 'INTERMEDIATE': {
			const intermediateTotal = density === 'low' ? 3 : density === 'medium' ? 4 : 5;
			return {
				totalErrors: intermediateTotal,
				mandatoryErrorTypes: [
					{
						type: 'preposition usage',
						minCount: 1,
						examples: [
							'good in English → good at English',
							'depend of → depend on',
							'interested for → interested in',
							'different than → different from',
						],
					},
					{
						type: 'word form errors',
						minCount: 1,
						examples: [
							'He was boring → He was bored',
							'I am interesting in music → I am interested in music',
							'She is very beauty → She is very beautiful',
							'This is importance → This is important',
						],
					},
					{
						type: 'conjunction errors',
						minCount: density === 'high' ? 1 : 0,
						examples: [
							'Although it was raining, but we went out → Although it was raining, we went out',
							'Because I was tired, so I slept → Because I was tired, I slept',
							'Despite of the rain → Despite the rain',
						],
					},
					{
						type: 'sentence fragments',
						minCount: density === 'high' ? 1 : 0,
						examples: [
							'Because I was tired. → Because I was tired, I went to bed early.',
							'When she arrived. → When she arrived, everyone was happy.',
							'Although he studied hard. → Although he studied hard, he failed the exam.',
						],
					},
				],
				description: `Include exactly ${intermediateTotal} errors with focus on intermediate-level grammar that requires systematic grammar knowledge to identify.`,
			};
		}

		case 'ADVANCED': {
			const advancedTotal = density === 'low' ? 4 : density === 'medium' ? 5 : 6;
			return {
				totalErrors: advancedTotal,
				mandatoryErrorTypes: [
					{
						type: 'reported speech',
						minCount: 1,
						examples: [
							'She said she is happy → She said she was happy',
							'He told me he will come → He told me he would come',
							'They said they are leaving → They said they were leaving',
						],
					},
					{
						type: 'conditional sentences',
						minCount: 1,
						examples: [
							'If I will go there → If I go there',
							'If I would have money → If I had money',
							'Unless you will not study → Unless you study',
						],
					},
					{
						type: 'word choice and semantics',
						minCount: 1,
						examples: [
							'He committed a mistake → He made a mistake',
							'She did a crime → She committed a crime',
							'I have a headache pain → I have a headache',
							'The price is expensive → The price is high',
						],
					},
					{
						type: 'sentence ambiguity',
						minCount: density === 'high' ? 1 : 0,
						examples: [
							'She told her friend that she loved her → She told her friend that she (the friend) loved her',
							'The man with the telescope saw the bird → The man saw the bird with the telescope',
							'Flying planes can be dangerous → Flying planes can be dangerous (ambiguous subject)',
						],
					},
					{
						type: 'register appropriateness',
						minCount: density === 'high' ? 1 : 0,
						examples: [
							'Hey dude, I want the job → Dear Sir/Madam, I am interested in the position',
							"That's totally awesome! → That is excellent.",
							'Gonna → Going to (in formal writing)',
						],
					},
				],
				description: `Include exactly ${advancedTotal} complex errors requiring deep grammatical understanding and contextual awareness to identify.`,
			};
		}

		default:
			return getDetailedErrorRequirements(density, 'INTERMEDIATE');
	}
}
