# Grammar Practice Tests

This directory contains comprehensive unit tests for the grammar practice functionality in the Vocab app.

## Test Coverage

The test suite covers all major components and flows of the grammar practice feature:

### 📁 Test Files

1. **`utils.test.ts`** - Tests for utility functions
   - Error color mapping
   - Edge cases and input validation

2. **`constants.test.ts`** - Tests for constants and configuration
   - Error type definitions
   - Difficulty level mappings
   - Error density descriptions

3. **`llm.service.test.ts`** - Tests for LLM service integration
   - Grammar practice generation
   - OpenAI API interaction
   - Error handling and retries
   - Parameter validation

4. **`use-grammar-practice.test.ts`** - Tests for React hook
   - State management
   - User interactions
   - Error handling
   - Integration with contexts

5. **`grammar-practice-client.test.tsx`** - Tests for main component
   - UI rendering
   - User interactions
   - Loading states
   - Error display

6. **`grammar-practice-item.test.tsx`** - Tests for practice item component
   - Error highlighting
   - Word selection
   - Language support
   - Accessibility

7. **`__tests__/grammar-practice.integration.test.tsx`** - Integration tests
   - End-to-end flow testing
   - API integration
   - Error scenarios
   - Authentication

## 🧪 Running Tests

### Run All Grammar Practice Tests
```bash
# Using Jest (recommended for full test suite)
yarn test grammar-practice

# Using Vitest (faster for unit tests)
yarn test:unit grammar-practice
```

### Run Specific Test Files
```bash
# Test utilities
yarn test utils.test.ts

# Test constants
yarn test constants.test.ts

# Test LLM service
yarn test llm.service.test.ts

# Test React hook
yarn test use-grammar-practice.test.ts

# Test components
yarn test grammar-practice-client.test.tsx
yarn test grammar-practice-item.test.tsx

# Test integration
yarn test grammar-practice.integration.test.tsx
```

### Run Tests in Watch Mode
```bash
# Watch all grammar practice tests
yarn test:watch --testPathPattern=grammar-practice

# Watch specific test file
yarn test:watch utils.test.ts
```

### Generate Coverage Report
```bash
# Coverage for all tests
yarn test:coverage

# Coverage for grammar practice only
yarn test:coverage --testPathPattern=grammar-practice
```

## 🎯 Test Scenarios Covered

### API Route Tests (`route.test.ts`)
- ✅ Authentication validation
- ✅ Input schema validation
- ✅ Error handling (401, 400, 500)
- ✅ Successful generation
- ✅ Development cache functionality
- ✅ Integration with LLM service

### Service Layer Tests (`llm.service.test.ts`)
- ✅ Grammar practice generation
- ✅ Different difficulty levels
- ✅ Error density handling
- ✅ Retry logic for API failures
- ✅ JSON parsing and validation
- ✅ Multiple language support

### Hook Tests (`use-grammar-practice.test.ts`)
- ✅ State initialization
- ✅ Parameter updates (difficulty, error density)
- ✅ Paragraph generation flow
- ✅ Error handling
- ✅ Word selection management
- ✅ Integration with contexts

### Component Tests
- ✅ UI rendering and layout
- ✅ User interactions
- ✅ Loading states
- ✅ Error display
- ✅ Form validation
- ✅ Accessibility compliance

### Integration Tests
- ✅ Complete user flow
- ✅ API integration
- ✅ Error scenarios
- ✅ Authentication flows
- ✅ State management across components

## 🔧 Test Configuration

### Mock Setup
The tests use comprehensive mocking:
- **MSW (Mock Service Worker)** for API endpoints
- **Jest mocks** for React contexts and hooks
- **Test fixtures** for consistent test data
- **OpenAI API mocks** for LLM service testing

### Test Data
Test fixtures are available in `@/test/fixtures`:
- `mockGrammarPracticeResult()` - Sample grammar practice response
- `mockGrammarPracticeParams()` - Sample request parameters
- `mockCollection()` - Sample collection data

### Environment Setup
Tests run with:
- **jsdom** environment for React component testing
- **Next.js** integration for API route testing
- **TypeScript** support for type safety
- **Coverage reporting** with 70% threshold

## 🐛 Debugging Tests

### Common Issues

1. **Mock not working**: Ensure mocks are properly cleared between tests
```typescript
beforeEach(() => {
  jest.clearAllMocks();
});
```

2. **Async test failures**: Use proper async/await patterns
```typescript
await waitFor(() => {
  expect(screen.getByText('Expected text')).toBeInTheDocument();
});
```

3. **Context not available**: Ensure all required contexts are mocked
```typescript
jest.mock('@/contexts', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
  useLLM: () => ({ generateGrammarPractice: jest.fn() }),
}));
```

### Debug Commands
```bash
# Run tests with verbose output
yarn test --verbose grammar-practice

# Run tests with debug information
yarn test --detectOpenHandles grammar-practice

# Run single test with full output
yarn test --testNamePattern="should generate grammar practice" --verbose
```

## 📊 Coverage Goals

The grammar practice tests aim for:
- **Lines**: >90% coverage
- **Functions**: >90% coverage
- **Branches**: >85% coverage
- **Statements**: >90% coverage

Current coverage can be viewed by running:
```bash
yarn test:coverage --testPathPattern=grammar-practice
```

## 🚀 Adding New Tests

When adding new grammar practice features:

1. **Add unit tests** for new functions/components
2. **Update integration tests** for new user flows
3. **Add test fixtures** for new data structures
4. **Update mocks** for new API endpoints
5. **Maintain coverage** above threshold levels

### Test Template
```typescript
describe('New Feature', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle the new functionality', () => {
    // Arrange
    const input = mockData();
    
    // Act
    const result = newFunction(input);
    
    // Assert
    expect(result).toEqual(expectedOutput);
  });
});
```
