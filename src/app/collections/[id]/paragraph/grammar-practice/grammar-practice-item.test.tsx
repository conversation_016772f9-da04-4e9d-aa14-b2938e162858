import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GrammarPracticeItem } from './grammar-practice-item';
import { mockGrammarPracticeResult } from '@/test/fixtures';
import { Language } from '@prisma/client';
import type { SelectedWord } from './types';

// Mock the contexts
jest.mock('@/contexts', () => ({
	useTranslation: () => ({
		t: (key: string) => key,
	}),
}));

describe('GrammarPracticeItem', () => {
	const user = userEvent.setup();
	const mockOnWordSelection = jest.fn();
	
	const defaultProps = {
		item: mockGrammarPracticeResult(),
		index: 0,
		selectedWords: [] as SelectedWord[],
		onWordSelection: mockOnWordSelection,
		targetLanguage: Language.EN,
		sourceLanguage: Language.VI,
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should render the practice item with paragraph', () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		expect(screen.getByText(/This is a test paragraph/)).toBeInTheDocument();
	});

	it('should render error information', () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		// Should show error count
		expect(screen.getByText(/1.*error/i)).toBeInTheDocument();
		
		// Should show error details
		expect(screen.getByText('grammer')).toBeInTheDocument();
		expect(screen.getByText('grammar')).toBeInTheDocument();
		expect(screen.getByText('spelling')).toBeInTheDocument();
	});

	it('should render explanations in both languages', () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		expect(screen.getByText(/Lỗi chính tả/)).toBeInTheDocument();
		expect(screen.getByText(/Spelling error/)).toBeInTheDocument();
	});

	it('should handle word selection when clicking on words', async () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		// Find a clickable word (this depends on how the component renders words)
		const wordElements = screen.getAllByRole('button');
		if (wordElements.length > 0) {
			await user.click(wordElements[0]);
			expect(mockOnWordSelection).toHaveBeenCalled();
		}
	});

	it('should highlight selected words', () => {
		const selectedWords: SelectedWord[] = [
			{ word: 'test', index: 5 },
			{ word: 'paragraph', index: 10 },
		];

		render(
			<GrammarPracticeItem
				{...defaultProps}
				selectedWords={selectedWords}
			/>
		);

		// Check if selected words have different styling
		// This would depend on the actual implementation
		expect(screen.getByText(/This is a test paragraph/)).toBeInTheDocument();
	});

	it('should show corrected paragraph when toggled', async () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		// Look for a toggle button or similar control
		const toggleButton = screen.getByRole('button', { name: /show.*correct/i });
		if (toggleButton) {
			await user.click(toggleButton);
			expect(screen.getByText(/This is a test paragraph with some grammar mistakes/)).toBeInTheDocument();
		}
	});

	it('should handle multiple errors', () => {
		const itemWithMultipleErrors = {
			...mockGrammarPracticeResult(),
			allErrors: [
				{
					errorText: 'grammer',
					correctedText: 'grammar',
					errorType: 'spelling',
					explanation: {
						source_language: 'Lỗi chính tả',
						target_language: 'Spelling error',
					},
				},
				{
					errorText: 'wrong',
					correctedText: 'incorrect',
					errorType: 'vocabulary',
					explanation: {
						source_language: 'Lỗi từ vựng',
						target_language: 'Vocabulary error',
					},
				},
			],
		};

		render(
			<GrammarPracticeItem
				{...defaultProps}
				item={itemWithMultipleErrors}
			/>
		);

		expect(screen.getByText(/2.*error/i)).toBeInTheDocument();
		expect(screen.getByText('spelling')).toBeInTheDocument();
		expect(screen.getByText('vocabulary')).toBeInTheDocument();
	});

	it('should handle different error types with appropriate colors', () => {
		const itemWithDifferentErrors = {
			...mockGrammarPracticeResult(),
			allErrors: [
				{
					errorText: 'test1',
					correctedText: 'corrected1',
					errorType: 'grammar',
					explanation: {
						source_language: 'Lỗi ngữ pháp',
						target_language: 'Grammar error',
					},
				},
				{
					errorText: 'test2',
					correctedText: 'corrected2',
					errorType: 'vocabulary',
					explanation: {
						source_language: 'Lỗi từ vựng',
						target_language: 'Vocabulary error',
					},
				},
			],
		};

		render(
			<GrammarPracticeItem
				{...defaultProps}
				item={itemWithDifferentErrors}
			/>
		);

		expect(screen.getByText('grammar')).toBeInTheDocument();
		expect(screen.getByText('vocabulary')).toBeInTheDocument();
	});

	it('should handle empty errors array', () => {
		const itemWithNoErrors = {
			...mockGrammarPracticeResult(),
			allErrors: [],
		};

		render(
			<GrammarPracticeItem
				{...defaultProps}
				item={itemWithNoErrors}
			/>
		);

		expect(screen.getByText(/0.*error/i)).toBeInTheDocument();
	});

	it('should handle different language combinations', () => {
		render(
			<GrammarPracticeItem
				{...defaultProps}
				targetLanguage={Language.VI}
				sourceLanguage={Language.EN}
			/>
		);

		// Should still render the content appropriately
		expect(screen.getByText(/This is a test paragraph/)).toBeInTheDocument();
	});

	it('should handle long paragraphs gracefully', () => {
		const itemWithLongParagraph = {
			...mockGrammarPracticeResult(),
			paragraphWithErrors: 'This is a very long paragraph '.repeat(20) + 'with errors.',
			correctedParagraph: 'This is a very long paragraph '.repeat(20) + 'without errors.',
		};

		render(
			<GrammarPracticeItem
				{...defaultProps}
				item={itemWithLongParagraph}
			/>
		);

		expect(screen.getByText(/This is a very long paragraph/)).toBeInTheDocument();
	});

	it('should handle special characters in text', () => {
		const itemWithSpecialChars = {
			...mockGrammarPracticeResult(),
			paragraphWithErrors: 'Text with special chars: @#$%^&*()[]{}|\\:";\'<>?,./',
			correctedParagraph: 'Text with special chars: @#$%^&*()[]{}|\\:";\'<>?,./',
		};

		render(
			<GrammarPracticeItem
				{...defaultProps}
				item={itemWithSpecialChars}
			/>
		);

		expect(screen.getByText(/Text with special chars/)).toBeInTheDocument();
	});

	it('should call onWordSelection with correct parameters', async () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		// This test would need to be more specific based on the actual implementation
		// of how words are rendered and made clickable
		const wordElements = screen.getAllByRole('button');
		if (wordElements.length > 0) {
			await user.click(wordElements[0]);
			expect(mockOnWordSelection).toHaveBeenCalledWith(
				defaultProps.index,
				expect.objectContaining({
					word: expect.any(String),
					index: expect.any(Number),
				})
			);
		}
	});

	it('should handle rapid word selections', async () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		const wordElements = screen.getAllByRole('button');
		if (wordElements.length >= 2) {
			await user.click(wordElements[0]);
			await user.click(wordElements[1]);
			
			expect(mockOnWordSelection).toHaveBeenCalledTimes(2);
		}
	});

	it('should maintain accessibility standards', () => {
		render(<GrammarPracticeItem {...defaultProps} />);

		// Check for proper ARIA labels and roles
		const buttons = screen.getAllByRole('button');
		buttons.forEach(button => {
			expect(button).toBeInTheDocument();
		});

		// Check for proper heading structure
		const headings = screen.getAllByRole('heading');
		expect(headings.length).toBeGreaterThan(0);
	});
});
