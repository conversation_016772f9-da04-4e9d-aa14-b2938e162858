import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { server } from '@/test/mocks/server';
import { http, HttpResponse } from 'msw';
import { GrammarPracticeClient } from '../grammar-practice-client';
import { mockCollection, mockGrammarPracticeResult } from '@/test/fixtures';

// Mock Next.js auth
jest.mock('@/lib', () => ({
	auth: jest.fn().mockResolvedValue({
		user: { id: 'test-user-id' },
	}),
}));

// Mock the contexts with real implementations
const mockKeywords = ['technology', 'innovation'];
const mockCollection = mockCollection();

jest.mock('@/contexts', () => ({
	useTranslation: () => ({
		t: (key: string) => key,
	}),
	useKeywordsContext: () => ({
		selectedKeywords: mockKeywords,
	}),
	useLLM: () => ({
		generateGrammarPractice: jest.fn(),
		isLoading: false,
		error: null,
	}),
}));

jest.mock('@/hooks', () => ({
	useCollections: () => ({
		currentCollection: mockCollection,
		loading: { get: false, setCurrent: false },
	}),
}));

jest.mock('@/hooks/use-page-guidance', () => ({
	usePageGuidance: jest.fn(),
}));

// Mock child components to focus on integration
jest.mock('../../components/keyword-form', () => ({
	KeywordForm: () => <div data-testid="keyword-form">Keyword Form</div>,
}));

jest.mock('../components', () => ({
	GenerationLoadingState: ({ titleKey }: { titleKey: string }) => (
		<div data-testid="loading-state">{titleKey}</div>
	),
	ParagraphPracticeSkeleton: () => <div data-testid="skeleton">Loading...</div>,
}));

jest.mock('../grammar-practice-item', () => ({
	GrammarPracticeItem: ({ item, index }: { item: any; index: number }) => (
		<div data-testid={`practice-item-${index}`}>
			<div>{item.paragraphWithErrors}</div>
			<div data-testid={`errors-${index}`}>
				{item.allErrors.length} errors found
			</div>
		</div>
	),
}));

describe('Grammar Practice Integration', () => {
	const user = userEvent.setup();

	beforeAll(() => {
		server.listen();
	});

	afterEach(() => {
		server.resetHandlers();
		jest.clearAllMocks();
	});

	afterAll(() => {
		server.close();
	});

	it('should complete the full grammar practice flow', async () => {
		// Mock successful API response
		server.use(
			http.post('/api/llm/generate-grammar-practice', () => {
				return HttpResponse.json([
					mockGrammarPracticeResult(),
					{
						...mockGrammarPracticeResult(),
						paragraphWithErrors: 'Another paragraph with diferent errors.',
						correctedParagraph: 'Another paragraph with different errors.',
						allErrors: [
							{
								errorText: 'diferent',
								correctedText: 'different',
								errorType: 'spelling',
								explanation: {
									source_language: 'Lỗi chính tả: "diferent" phải là "different"',
									target_language: 'Spelling error: "diferent" should be "different"',
								},
							},
						],
					},
				]);
			})
		);

		// Mock the LLM context to actually call the API
		const mockGenerateGrammarPractice = jest.fn().mockImplementation(async (params) => {
			const response = await fetch('/api/llm/generate-grammar-practice', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(params),
			});
			return response.json();
		});

		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: false,
			error: null,
		});

		render(<GrammarPracticeClient />);

		// Verify initial state
		expect(screen.getByText('grammar.practice_title')).toBeInTheDocument();
		expect(screen.getByTestId('keyword-form')).toBeInTheDocument();

		// Click generate button
		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		expect(generateButton).not.toBeDisabled();

		await user.click(generateButton);

		// Wait for API call and results
		await waitFor(() => {
			expect(mockGenerateGrammarPractice).toHaveBeenCalledWith({
				keywords: mockKeywords,
				language: mockCollection.target_language,
				source_language: mockCollection.source_language,
				target_language: mockCollection.target_language,
				difficulty: 'INTERMEDIATE',
				count: 5,
				errorDensity: 'medium',
			});
		});

		// Verify results are displayed
		await waitFor(() => {
			expect(screen.getByTestId('practice-item-0')).toBeInTheDocument();
			expect(screen.getByTestId('practice-item-1')).toBeInTheDocument();
		});

		// Verify content is displayed
		expect(screen.getByText(/This is a test paragraph with some grammer mistakes/)).toBeInTheDocument();
		expect(screen.getByText(/Another paragraph with diferent errors/)).toBeInTheDocument();

		// Verify error counts
		expect(screen.getByTestId('errors-0')).toHaveTextContent('1 errors found');
		expect(screen.getByTestId('errors-1')).toHaveTextContent('1 errors found');
	});

	it('should handle API errors gracefully', async () => {
		// Mock API error
		server.use(
			http.post('/api/llm/generate-grammar-practice', () => {
				return HttpResponse.json(
					{ error: 'Internal server error' },
					{ status: 500 }
				);
			})
		);

		const mockGenerateGrammarPractice = jest.fn().mockRejectedValue(
			new Error('Failed to generate grammar practice')
		);

		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: false,
			error: null,
		});

		render(<GrammarPracticeClient />);

		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		await user.click(generateButton);

		await waitFor(() => {
			expect(mockGenerateGrammarPractice).toHaveBeenCalled();
		});

		// Should show error message
		await waitFor(() => {
			expect(screen.getByText(/Failed to generate grammar practice/)).toBeInTheDocument();
		});

		// Should not show practice items
		expect(screen.queryByTestId('practice-item-0')).not.toBeInTheDocument();
	});

	it('should handle authentication errors', async () => {
		// Mock authentication failure
		server.use(
			http.post('/api/llm/generate-grammar-practice', () => {
				return HttpResponse.json(
					{ error: 'User not authenticated for generating paragraphs.' },
					{ status: 401 }
				);
			})
		);

		const mockGenerateGrammarPractice = jest.fn().mockRejectedValue(
			new Error('User not authenticated for generating paragraphs.')
		);

		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: false,
			error: null,
		});

		render(<GrammarPracticeClient />);

		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		await user.click(generateButton);

		await waitFor(() => {
			expect(screen.getByText(/User not authenticated/)).toBeInTheDocument();
		});
	});

	it('should handle validation errors', async () => {
		// Mock validation error
		server.use(
			http.post('/api/llm/generate-grammar-practice', () => {
				return HttpResponse.json(
					{ error: 'At least one keyword is required' },
					{ status: 400 }
				);
			})
		);

		// Mock empty keywords
		jest.mocked(require('@/contexts').useKeywordsContext).mockReturnValue({
			selectedKeywords: [],
		});

		const mockGenerateGrammarPractice = jest.fn().mockRejectedValue(
			new Error('No keywords selected')
		);

		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: false,
			error: null,
		});

		render(<GrammarPracticeClient />);

		// Button should be disabled with no keywords
		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		expect(generateButton).toBeDisabled();
	});

	it('should show loading state during generation', async () => {
		let resolvePromise: (value: any) => void;
		const mockPromise = new Promise((resolve) => {
			resolvePromise = resolve;
		});

		const mockGenerateGrammarPractice = jest.fn().mockReturnValue(mockPromise);

		// Mock loading state
		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: true,
			error: null,
		});

		render(<GrammarPracticeClient />);

		// Should show loading state
		expect(screen.getByTestId('loading-state')).toBeInTheDocument();
		expect(screen.getByText('grammar.generating_paragraphs')).toBeInTheDocument();

		// Button should be disabled and show loading text
		const generateButton = screen.getByRole('button', { name: /grammar.generating/i });
		expect(generateButton).toBeDisabled();

		// Resolve the promise
		resolvePromise!([mockGrammarPracticeResult()]);

		// Update mock to not loading
		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: false,
			error: null,
		});
	});

	it('should handle different difficulty levels', async () => {
		const mockGenerateGrammarPractice = jest.fn().mockResolvedValue([mockGrammarPracticeResult()]);

		jest.mocked(require('@/contexts').useLLM).mockReturnValue({
			generateGrammarPractice: mockGenerateGrammarPractice,
			isLoading: false,
			error: null,
		});

		render(<GrammarPracticeClient />);

		// This would require more complex interaction with Select components
		// For now, we verify the default difficulty is used
		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		await user.click(generateButton);

		await waitFor(() => {
			expect(mockGenerateGrammarPractice).toHaveBeenCalledWith(
				expect.objectContaining({
					difficulty: 'INTERMEDIATE',
				})
			);
		});
	});
});
