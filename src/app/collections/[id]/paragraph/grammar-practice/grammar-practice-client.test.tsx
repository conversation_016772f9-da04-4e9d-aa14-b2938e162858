import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GrammarPracticeClient } from './grammar-practice-client';
import { mockCollection, mockGrammarPracticeResult } from '@/test/fixtures';
import { Difficulty } from '@prisma/client';

// Mock the hooks and contexts
const mockGenerateParagraphs = jest.fn();
const mockSetDifficulty = jest.fn();
const mockSetErrorDensity = jest.fn();
const mockToggleWordSelection = jest.fn();

jest.mock('./use-grammar-practice', () => ({
	useGrammarPractice: () => ({
		difficulty: Difficulty.INTERMEDIATE,
		setDifficulty: mockSetDifficulty,
		isLoading: false,
		errorDensity: 'medium',
		generateParagraphs: mockGenerateParagraphs,
		setErrorDensity: mockSetErrorDensity,
		selectedKeywords: ['technology', 'innovation'],
		hasError: false,
		errorMessage: null,
		paragraphs: [],
		selections: {},
		toggleWordSelection: mockToggleWordSelection,
	}),
}));

jest.mock('@/contexts', () => ({
	useTranslation: () => ({
		t: (key: string) => key,
	}),
}));

jest.mock('@/hooks', () => ({
	useCollections: () => ({
		currentCollection: mockCollection(),
		loading: { get: false, setCurrent: false },
	}),
}));

jest.mock('@/hooks/use-page-guidance', () => ({
	usePageGuidance: jest.fn(),
}));

// Mock the child components
jest.mock('../../components/keyword-form', () => ({
	KeywordForm: () => <div data-testid="keyword-form">Keyword Form</div>,
}));

jest.mock('../components', () => ({
	GenerationLoadingState: ({ titleKey }: { titleKey: string }) => (
		<div data-testid="loading-state">{titleKey}</div>
	),
	ParagraphPracticeSkeleton: () => <div data-testid="skeleton">Loading...</div>,
}));

jest.mock('./grammar-practice-item', () => ({
	GrammarPracticeItem: ({ item, index }: { item: any; index: number }) => (
		<div data-testid={`practice-item-${index}`}>Practice Item {index}</div>
	),
}));

describe('GrammarPracticeClient', () => {
	const user = userEvent.setup();

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should render the main interface', () => {
		render(<GrammarPracticeClient />);

		expect(screen.getByText('grammar.practice_title')).toBeInTheDocument();
		expect(screen.getByText('grammar.practice_description')).toBeInTheDocument();
		expect(screen.getByText('Practice Configuration')).toBeInTheDocument();
		expect(screen.getByTestId('keyword-form')).toBeInTheDocument();
	});

	it('should render difficulty selector', () => {
		render(<GrammarPracticeClient />);

		expect(screen.getByText('grammar.difficulty_label')).toBeInTheDocument();
		// The Select component should be present
		expect(screen.getByRole('combobox')).toBeInTheDocument();
	});

	it('should render error density selector', () => {
		render(<GrammarPracticeClient />);

		expect(screen.getByText('grammar.error_density_label')).toBeInTheDocument();
		// Should have two comboboxes (difficulty and error density)
		expect(screen.getAllByRole('combobox')).toHaveLength(2);
	});

	it('should render generate button', () => {
		render(<GrammarPracticeClient />);

		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		expect(generateButton).toBeInTheDocument();
		expect(generateButton).not.toBeDisabled();
	});

	it('should disable generate button when no keywords selected', () => {
		// Mock empty keywords
		jest.mocked(require('./use-grammar-practice').useGrammarPractice).mockReturnValue({
			difficulty: Difficulty.INTERMEDIATE,
			setDifficulty: mockSetDifficulty,
			isLoading: false,
			errorDensity: 'medium',
			generateParagraphs: mockGenerateParagraphs,
			setErrorDensity: mockSetErrorDensity,
			selectedKeywords: [],
			hasError: false,
			errorMessage: null,
			paragraphs: [],
			selections: {},
			toggleWordSelection: mockToggleWordSelection,
		});

		render(<GrammarPracticeClient />);

		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		expect(generateButton).toBeDisabled();
	});

	it('should disable generate button when loading', () => {
		jest.mocked(require('./use-grammar-practice').useGrammarPractice).mockReturnValue({
			difficulty: Difficulty.INTERMEDIATE,
			setDifficulty: mockSetDifficulty,
			isLoading: true,
			errorDensity: 'medium',
			generateParagraphs: mockGenerateParagraphs,
			setErrorDensity: mockSetErrorDensity,
			selectedKeywords: ['technology'],
			hasError: false,
			errorMessage: null,
			paragraphs: [],
			selections: {},
			toggleWordSelection: mockToggleWordSelection,
		});

		render(<GrammarPracticeClient />);

		const generateButton = screen.getByRole('button', { name: /grammar.generating/i });
		expect(generateButton).toBeDisabled();
	});

	it('should call generateParagraphs when generate button is clicked', async () => {
		render(<GrammarPracticeClient />);

		const generateButton = screen.getByRole('button', { name: /grammar.view_result_button/i });
		await user.click(generateButton);

		expect(mockGenerateParagraphs).toHaveBeenCalledTimes(1);
	});

	it('should show loading state when generating', () => {
		jest.mocked(require('./use-grammar-practice').useGrammarPractice).mockReturnValue({
			difficulty: Difficulty.INTERMEDIATE,
			setDifficulty: mockSetDifficulty,
			isLoading: true,
			errorDensity: 'medium',
			generateParagraphs: mockGenerateParagraphs,
			setErrorDensity: mockSetErrorDensity,
			selectedKeywords: ['technology'],
			hasError: false,
			errorMessage: null,
			paragraphs: [],
			selections: {},
			toggleWordSelection: mockToggleWordSelection,
		});

		render(<GrammarPracticeClient />);

		expect(screen.getByTestId('loading-state')).toBeInTheDocument();
		expect(screen.getByText('grammar.generating_paragraphs')).toBeInTheDocument();
	});

	it('should show error message when there is an error', () => {
		jest.mocked(require('./use-grammar-practice').useGrammarPractice).mockReturnValue({
			difficulty: Difficulty.INTERMEDIATE,
			setDifficulty: mockSetDifficulty,
			isLoading: false,
			errorDensity: 'medium',
			generateParagraphs: mockGenerateParagraphs,
			setErrorDensity: mockSetErrorDensity,
			selectedKeywords: ['technology'],
			hasError: true,
			errorMessage: 'Test error message',
			paragraphs: [],
			selections: {},
			toggleWordSelection: mockToggleWordSelection,
		});

		render(<GrammarPracticeClient />);

		expect(screen.getByText('Test error message')).toBeInTheDocument();
	});

	it('should render practice items when paragraphs are available', () => {
		const mockParagraphs = [mockGrammarPracticeResult(), mockGrammarPracticeResult()];
		
		jest.mocked(require('./use-grammar-practice').useGrammarPractice).mockReturnValue({
			difficulty: Difficulty.INTERMEDIATE,
			setDifficulty: mockSetDifficulty,
			isLoading: false,
			errorDensity: 'medium',
			generateParagraphs: mockGenerateParagraphs,
			setErrorDensity: mockSetErrorDensity,
			selectedKeywords: ['technology'],
			hasError: false,
			errorMessage: null,
			paragraphs: mockParagraphs,
			selections: {},
			toggleWordSelection: mockToggleWordSelection,
		});

		render(<GrammarPracticeClient />);

		expect(screen.getByTestId('practice-item-0')).toBeInTheDocument();
		expect(screen.getByTestId('practice-item-1')).toBeInTheDocument();
	});

	it('should show skeleton when collection is loading', () => {
		jest.mocked(require('@/hooks').useCollections).mockReturnValue({
			currentCollection: null,
			loading: { get: true, setCurrent: false },
		});

		render(<GrammarPracticeClient />);

		expect(screen.getByTestId('skeleton')).toBeInTheDocument();
	});

	it('should show skeleton when collection is not available', () => {
		jest.mocked(require('@/hooks').useCollections).mockReturnValue({
			currentCollection: null,
			loading: { get: false, setCurrent: false },
		});

		render(<GrammarPracticeClient />);

		expect(screen.getByTestId('skeleton')).toBeInTheDocument();
	});

	it('should handle difficulty change', async () => {
		render(<GrammarPracticeClient />);

		// This would require more complex interaction with the Select component
		// For now, we'll test that the difficulty value is displayed correctly
		expect(screen.getByDisplayValue('INTERMEDIATE')).toBeInTheDocument();
	});

	it('should handle error density change', async () => {
		render(<GrammarPracticeClient />);

		// Similar to difficulty, this would require Select component interaction
		// We can verify the current value is displayed
		expect(screen.getByDisplayValue('medium')).toBeInTheDocument();
	});

	it('should not render practice items when loading', () => {
		jest.mocked(require('./use-grammar-practice').useGrammarPractice).mockReturnValue({
			difficulty: Difficulty.INTERMEDIATE,
			setDifficulty: mockSetDifficulty,
			isLoading: true,
			errorDensity: 'medium',
			generateParagraphs: mockGenerateParagraphs,
			setErrorDensity: mockSetErrorDensity,
			selectedKeywords: ['technology'],
			hasError: false,
			errorMessage: null,
			paragraphs: [mockGrammarPracticeResult()],
			selections: {},
			toggleWordSelection: mockToggleWordSelection,
		});

		render(<GrammarPracticeClient />);

		expect(screen.queryByTestId('practice-item-0')).not.toBeInTheDocument();
		expect(screen.getByTestId('loading-state')).toBeInTheDocument();
	});

	it('should not render practice items when no paragraphs', () => {
		render(<GrammarPracticeClient />);

		expect(screen.queryByTestId('practice-item-0')).not.toBeInTheDocument();
	});
});
