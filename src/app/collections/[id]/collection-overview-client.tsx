'use client';

import { InteractiveTutorial } from '@/components/onboarding';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	ErrorDisplay,
	NavigationCard,
	Translate,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useCollections } from '@/hooks';
import {
	CheckCircle2,
	Edit3,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	TrendingUp,
	Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Navigation Groups Section Skeleton */}
			<div className="space-y-6 sm:space-y-8">
				<div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
					<div className="h-8 w-48 bg-muted rounded animate-pulse" />
					<div className="hidden sm:block h-px flex-1 bg-muted animate-pulse" />
					<div className="h-10 w-32 bg-muted rounded animate-pulse" />
				</div>

				{/* Navigation Groups Skeleton */}
				{Array.from({ length: 3 }).map((_, groupIndex) => (
					<div key={groupIndex} className="space-y-3">
						{/* Group Header Skeleton */}
						<div className="space-y-1">
							<div className="h-6 w-48 bg-muted rounded animate-pulse" />
							<div className="h-4 w-72 bg-muted/70 rounded animate-pulse" />
						</div>

						{/* Group Cards Skeleton */}
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
							{Array.from({ length: groupIndex === 2 ? 3 : 2 }).map(
								(_, cardIndex) => (
									<Card key={cardIndex} className="relative overflow-hidden h-36">
										<div className="absolute inset-0 bg-gradient-to-br from-muted/20 to-muted/10 animate-pulse" />
										<CardContent className="relative z-20 p-4 h-full flex flex-col">
											{/* Icon skeleton */}
											<div className="mb-3">
												<div className="w-10 h-10 bg-muted rounded-lg animate-pulse" />
											</div>
											{/* Content skeleton */}
											<div className="flex-1 space-y-1">
												<div className="h-5 w-24 bg-muted rounded animate-pulse" />
												<div className="h-3 w-full bg-muted/70 rounded animate-pulse" />
												<div className="h-3 w-3/4 bg-muted/70 rounded animate-pulse" />
											</div>
										</CardContent>
									</Card>
								)
							)}
						</div>
					</div>
				))}
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();
	const { t } = useTranslation();

	// Tutorial state
	const [showTutorial, setShowTutorial] = useState(false);

	const wordCount = currentCollection?.word_ids.length || 0;

	const handleTutorialComplete = () => {
		setShowTutorial(false);
		if (currentCollection) {
			localStorage.setItem(`tutorial-completed-${currentCollection.id}`, 'true');
		}
	};

	const getStepStatus = (stepNumber: number): 'available' | 'locked' => {
		switch (stepNumber) {
			case 1: // Vocabulary step
				return 'available';
			case 2: // Paragraph step
				return 'available';
			default:
				return 'locked';
		}
	};

	const getVocabStepStatus = (stepNumber: number): 'available' | 'locked' => {
		switch (stepNumber) {
			case 1: // Add words
				return 'available';
			case 2: // Browse words
				return wordCount === 0 ? 'locked' : 'available';
			case 3: // Study flashcards
				return wordCount === 0 ? 'locked' : 'available';
			case 4: // Take quiz
				return wordCount < 10 ? 'locked' : 'available';
			default:
				return 'locked';
		}
	};

	const getVocabStepLockedReason = (stepNumber: number): string | undefined => {
		switch (stepNumber) {
			case 2: // Browse words
				return wordCount === 0 ? t('collections.vocab.step2_locked_reason') : undefined;
			case 3: // Study flashcards
				return wordCount === 0 ? t('collections.vocab.step3_locked_reason') : undefined;
			case 4: // Take quiz
				return wordCount < 10 ? t('collections.vocab.step4_locked_reason') : undefined;
			default:
				return undefined;
		}
	};

	// Navigation options organized by groups
	const navigationGroups = currentCollection
		? [
				{
					title: t('collections.flow.vocabulary_practice_title'),
					description: t('collections.flow.vocabulary_practice_message'),
					options: [
						{
							title: t('collections.vocab.step3_title'),
							description: t('collections.vocab.step3_message'),
							icon: RefreshCw,
							status: getVocabStepStatus(3),
							href: `/collections/${currentCollection.id}/vocabulary/review`,
							lockedReason: getVocabStepLockedReason(3),
							gradient: 'from-green-500/10 to-emerald-500/5',
						},
						{
							title: t('collections.vocab.step4_title'),
							description: t('collections.vocab.step4_message'),
							icon: Target,
							status: getVocabStepStatus(4),
							href: `/collections/${currentCollection.id}/vocabulary/mcq`,
							lockedReason: getVocabStepLockedReason(4),
							gradient: 'from-purple-500/10 to-violet-500/5',
						},
					],
				},
				{
					title: t('collections.flow.paragraph_practice_title'),
					description: t('collections.flow.paragraph_practice_message'),
					options: [
						{
							title: t('collections.tabs.paragraph_practice'),
							description: t('collections.overview.paragraph_practice_desc'),
							icon: Edit3,
							status: getStepStatus(2),
							href: `/collections/${currentCollection.id}/paragraph/paragraph-practice`,
							lockedReason:
								wordCount < 10
									? t('collections.paragraph.locked_reason')
									: undefined,
							gradient: 'from-orange-500/10 to-red-500/5',
						},
						{
							title: t('qa_practice.tab_title'),
							description: t('collections.overview.qa_practice_desc'),
							icon: MessageSquare,
							status: getStepStatus(2),
							href: `/collections/${currentCollection.id}/paragraph/qa-practice`,
							lockedReason:
								wordCount < 10
									? t('collections.paragraph.locked_reason')
									: undefined,
							gradient: 'from-pink-500/10 to-rose-500/5',
						},
						{
							title: t('collections.tabs.grammar_practice'),
							description: t('collections.overview.grammar_practice_desc'),
							icon: CheckCircle2,
							status: getStepStatus(2),
							href: `/collections/${currentCollection.id}/paragraph/grammar-practice`,
							lockedReason:
								wordCount < 10
									? t('collections.paragraph.locked_reason')
									: undefined,
							gradient: 'from-teal-500/10 to-cyan-500/5',
						},
					],
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			{/* Interactive Tutorial */}
			<InteractiveTutorial
				isOpen={showTutorial}
				onClose={() => setShowTutorial(false)}
				onComplete={handleTutorialComplete}
			/>

			<div className="min-h-screen">
				<div className="w-full pb-4 sm:pb-6 lg:pb-8 space-y-4 sm:space-y-6 lg:space-y-8">
					{/* Navigation Groups Section */}
					<div className="space-y-6 sm:space-y-8">
						{/* Action Buttons Panel */}
						<div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
							<div className="hidden sm:block h-px flex-1 bg-gradient-to-r from-border to-transparent" />
							<div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
								<Link
									href={`/collections/${currentCollection.id}/vocabulary/generate`}
								>
									<Button
										variant="outline"
										size="sm"
										className="flex items-center gap-2 w-full sm:w-auto"
										disabled={getVocabStepStatus(1) === 'locked'}
									>
										<Zap className="h-4 w-4" />
										<Translate text="collections.vocab.step1_title" />
									</Button>
								</Link>
								<Link
									href={`/collections/${currentCollection.id}/vocabulary/my-words`}
								>
									<Button
										variant="outline"
										size="sm"
										className="flex items-center gap-2 w-full sm:w-auto"
										disabled={getVocabStepStatus(2) === 'locked'}
									>
										<ListChecks className="h-4 w-4" />
										<Translate text="collections.vocab.step2_title" />
									</Button>
								</Link>
								<Link href={`/collections/${currentCollection.id}/stats`}>
									<Button
										variant="outline"
										size="sm"
										className="flex items-center gap-2 w-full sm:w-auto"
									>
										<TrendingUp className="h-4 w-4" />
										<Translate text="collections.stats.title" />
									</Button>
								</Link>
							</div>
						</div>

						{/* Navigation Groups */}
						{navigationGroups.map((group, groupIndex) => (
							<div key={group.title} className="space-y-3">
								{/* Group Header */}
								<div className="space-y-1">
									<h3 className="text-lg font-semibold text-foreground">
										{group.title}
									</h3>
									<p className="text-sm text-muted-foreground">
										{group.description}
									</p>
								</div>

								{/* Group Cards */}
								<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
									{group.options.map((option, optionIndex) => (
										<NavigationCard
											key={option.href}
											title={option.title}
											description={option.description}
											icon={option.icon}
											href={option.href}
											status={option.status}
											lockedReason={option.lockedReason}
											gradient={option.gradient}
											delay={groupIndex * 0.2 + optionIndex * 0.1}
										/>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</>
	);
}
