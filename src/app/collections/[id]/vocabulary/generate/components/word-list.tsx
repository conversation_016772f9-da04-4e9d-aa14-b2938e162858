'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Loading<PERSON>pinner,
	Translate,
} from '@/components/ui';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { RandomWord, WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { Plus, Undo2 } from 'lucide-react';
import { memo, useCallback } from 'react';

interface WordItemProps {
	word: RandomWord;
	details: WordDetail | undefined;
	loadingState: { gettingDetail?: boolean; adding?: boolean; generatingExamples?: boolean };
	onGetDetails: (word: RandomWord) => Promise<void>;
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoAddWord: (word: RandomWord) => Promise<void>;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	isAdded: boolean;
	sourceLanguage: Language;
	targetLanguage: Language;
}

function WordItem({
	word,
	details,
	loadingState,
	onGetDetails,
	onAddToCollection,
	onUndoAddWord,
	onGenerateExamples,
	isAdded,
	sourceLanguage,
	targetLanguage,
}: WordItemProps) {
	return (
		<Card
			key={word.term}
			className="flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200"
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<div className="flex-1">
						<h3 className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm mb-2">
							{word.term}
						</h3>
						{word.partOfSpeech && word.partOfSpeech.length > 0 && (
							<div className="flex flex-wrap gap-1">
								{word.partOfSpeech.map((pos, index) => (
									<Badge
										key={index}
										variant="secondary"
										className="text-xs font-medium"
									>
										{pos}
									</Badge>
								))}
							</div>
						)}
					</div>
				</CardTitle>
			</CardHeader>
			<CardContent className="flex-grow flex flex-col p-5">
				<div className="space-y-4 flex-grow">
					{/* Basic meaning from RandomWord */}
					{!details && word.meaning && word.meaning.length > 0 && (
						<div className="space-y-2">
							{word.meaning.map((meaning: Record<Language, string>, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									<div className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1">
										<p className="text-xs font-medium text-muted-foreground tracking-wide">
											<Translate
												text={getTranslationKeyOfLanguage(targetLanguage)}
											/>
											:
										</p>
										<p className="mb-1 text-sm text-foreground/95">
											{meaning[targetLanguage] || (
												<span className="italic opacity-70">
													Meaning not provided
												</span>
											)}
										</p>
										<p className="text-xs font-medium text-muted-foreground tracking-wide">
											<Translate
												text={getTranslationKeyOfLanguage(sourceLanguage)}
											/>
											:
										</p>
										<p className="text-sm text-foreground/95">
											{meaning[sourceLanguage] || (
												<span className="italic opacity-70">
													Translation not provided
												</span>
											)}
										</p>
									</div>
								</div>
							))}
						</div>
					)}

					{/* Detailed information from WordDetail */}
					{details && details.definitions && details.definitions.length > 0
						? details.definitions.map((definition, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									{definition.pos && definition.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{definition.pos.join(', ')}
										</p>
									)}
									{definition.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {definition.ipa}
										</p>
									)}
									{/* Explanations section */}
									{definition.explains && definition.explains.length > 0 ? (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Explanations:
											</p>
											{definition.explains.map((explain, expIndex) => (
												<div
													key={expIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{explain[targetLanguage] || (
															<span className="italic opacity-70">
																Explanation not provided
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													<p className="text-sm text-foreground/95">
														{explain[sourceLanguage] || (
															<span className="italic opacity-70">
																Translation not provided
															</span>
														)}
													</p>
												</div>
											))}
										</div>
									) : (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Explanations:
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												No explanations provided for this definition.
											</p>
										</div>
									)}

									{/* Examples section */}
									{definition.examples && definition.examples.length > 0 ? (
										<div>
											<div className="flex items-center justify-between mb-1.5">
												<p className="text-sm font-semibold text-muted-foreground">
													Examples:
												</p>
												{onGenerateExamples && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() => onGenerateExamples(word)}
														disabled={loadingState.generatingExamples}
														className="h-6 px-2 text-xs"
													>
														{loadingState.generatingExamples ? (
															<LoadingSpinner size="sm" />
														) : (
															<Plus className="h-3 w-3" />
														)}
														<span className="ml-1">
															{loadingState.generatingExamples
																? 'Generating...'
																: 'More'}
														</span>
													</Button>
												)}
											</div>
											{definition.examples.map((example, exIndex) => (
												<div
													key={exIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{example[targetLanguage] || (
															<span className="italic opacity-70">
																Example not provided
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													<p className="text-sm text-foreground/95">
														{example[sourceLanguage] || (
															<span className="italic opacity-70">
																Translation not provided
															</span>
														)}
													</p>
												</div>
											))}
										</div>
									) : (
										<div>
											<div className="flex items-center justify-between mb-1.5">
												<p className="text-sm font-semibold text-muted-foreground">
													Examples:
												</p>
												{onGenerateExamples && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() => onGenerateExamples(word)}
														disabled={loadingState.generatingExamples}
														className="h-6 px-2 text-xs"
													>
														{loadingState.generatingExamples ? (
															<LoadingSpinner size="sm" />
														) : (
															<Plus className="h-3 w-3" />
														)}
														<span className="ml-1">
															{loadingState.generatingExamples
																? 'Generating...'
																: 'Generate'}
														</span>
													</Button>
												)}
											</div>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												No examples provided for this definition.
											</p>
										</div>
									)}
								</div>
						  ))
						: details && (
								<p className="p-4 text-sm text-muted-foreground italic">
									No definitions available.
								</p>
						  )}
				</div>

				{/* Action buttons */}
				<div className="mt-auto flex flex-col space-y-2 pt-5">
					{!details && !loadingState.gettingDetail && (
						<Button
							variant="outline"
							size="sm"
							onClick={() => onGetDetails(word)}
							className="w-full flex items-center justify-center gap-2"
						>
							<Translate text="words.get_details" />
						</Button>
					)}
					{loadingState.gettingDetail && (
						<Button
							variant="outline"
							size="sm"
							disabled
							className="w-full flex items-center justify-center gap-2"
						>
							<LoadingSpinner size="sm" />
							<span>Getting Details...</span>
						</Button>
					)}
					{isAdded ? (
						<Button
							variant="outline"
							size="sm"
							onClick={() => onUndoAddWord(word)}
							className="w-full flex items-center justify-center gap-2 border-orange-200 text-orange-700 hover:bg-orange-50 hover:border-orange-300"
						>
							<Undo2 size={16} />
							<span>Undo</span>
						</Button>
					) : (
						<Button
							variant="default"
							size="sm"
							onClick={() => onAddToCollection(word)}
							disabled={loadingState.adding}
							className="w-full flex items-center justify-center gap-2"
						>
							{loadingState.adding ? (
								<LoadingSpinner size="sm" />
							) : (
								<Plus size={16} />
							)}
							<span>{loadingState.adding ? 'Adding...' : 'Add to Collection'}</span>
						</Button>
					)}
				</div>
			</CardContent>
		</Card>
	);
}

interface WordListProps {
	words: RandomWord[] | undefined;
	detailedWords: Record<string, WordDetail>;
	onGetDetails: (word: RandomWord) => Promise<void>;
	getLoadingState: (term: string) => {
		gettingDetail?: boolean;
		adding?: boolean;
		generatingExamples?: boolean;
	};
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoAddWord: (word: RandomWord) => Promise<void>;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	addedWords: Set<string>;
	className?: string;
	sourceLanguage: Language;
	targetLanguage: Language;
	// Load more functionality
	isLoadingMore?: boolean;
	onLoadMore?: () => Promise<void>;
}

function WordListComponent({
	words,
	detailedWords,
	onGetDetails,
	getLoadingState,
	onAddToCollection,
	onUndoAddWord,
	onGenerateExamples,
	addedWords,
	className,
	sourceLanguage, // Default fallback
	targetLanguage, // Default fallback
	isLoadingMore = false,
	onLoadMore,
}: WordListProps) {
	const handleGetDetails = useCallback(
		async (word: RandomWord) => {
			if (detailedWords[word.term] || getLoadingState(word.term).gettingDetail) return;
			await onGetDetails(word);
		},
		[detailedWords, getLoadingState, onGetDetails]
	);

	const handleAddToCollection = useCallback(
		async (word: RandomWord) => {
			if (getLoadingState(word.term).adding) return;
			await onAddToCollection(word);
		},
		[getLoadingState, onAddToCollection]
	);

	const handleUndoAddWord = useCallback(
		async (word: RandomWord) => {
			await onUndoAddWord(word);
		},
		[onUndoAddWord]
	);

	if (!words || words.length === 0) return null;

	return (
		<div className={cn('space-y-4', className)}>
			{words?.map((word) => {
				const loadingState = getLoadingState(word.term);
				const details = detailedWords[word.term];

				return (
					<WordItem
						key={word.term}
						word={word}
						details={details}
						loadingState={loadingState}
						onGetDetails={handleGetDetails}
						onAddToCollection={handleAddToCollection}
						onUndoAddWord={handleUndoAddWord}
						onGenerateExamples={onGenerateExamples}
						isAdded={addedWords.has(word.term)}
						sourceLanguage={sourceLanguage}
						targetLanguage={targetLanguage}
					/>
				);
			})}

			{/* Load More Button - Always show if onLoadMore is provided */}
			{onLoadMore && (
				<div className="flex justify-center pt-6">
					<Button
						variant="outline"
						size="lg"
						onClick={onLoadMore}
						disabled={isLoadingMore}
						className="min-w-[200px] h-12 text-base font-medium rounded-xl border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200"
					>
						{isLoadingMore ? (
							<>
								<LoadingSpinner size="sm" className="mr-2" />
								<Translate text="words.loading_more" />
							</>
						) : (
							<>
								<Plus size={18} className="mr-2" />
								<Translate text="words.load_more" />
							</>
						)}
					</Button>
				</div>
			)}
		</div>
	);
}

const arePropsEqual = (prevProps: WordListProps, nextProps: WordListProps) => {
	return (
		(prevProps.words?.length ?? 0) === (nextProps.words?.length ?? 0) &&
		(prevProps.words?.every((word, index) => word.term === nextProps.words?.[index]?.term) ??
			true) &&
		Object.keys(prevProps.detailedWords).length ===
			Object.keys(nextProps.detailedWords).length &&
		prevProps.className === nextProps.className &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.isLoadingMore === nextProps.isLoadingMore &&
		// Shallow compare functions assuming they are stable (e.g., from useCallback with stable deps)
		prevProps.onGetDetails === nextProps.onGetDetails &&
		prevProps.getLoadingState === nextProps.getLoadingState &&
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onLoadMore === nextProps.onLoadMore
	);
};

export const WordList = memo(WordListComponent, arePropsEqual);
