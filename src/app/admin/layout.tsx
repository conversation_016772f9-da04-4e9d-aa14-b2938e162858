'use client';

import { AdminAuthProvider } from '@/contexts/admin-auth-context';
import { ToastProvider } from '@/contexts/toast-context';
import { TranslationProvider } from '@/contexts/translation-context';
import { ThemeProvider } from '@/components/ui/theme-provider';

interface AdminRootLayoutProps {
	children: React.ReactNode;
}

export default function AdminRootLayout({ children }: AdminRootLayoutProps) {
	return (
		<ThemeProvider>
			<TranslationProvider>
				<ToastProvider>
					<AdminAuthProvider>{children}</AdminAuthProvider>
				</ToastProvider>
			</TranslationProvider>
		</ThemeProvider>
	);
}
