'use client';

import { HeroSection } from '@/components/home';
import { Button, ScreenReaderAnnouncement, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { ArrowRight, BookOpen } from 'lucide-react';
import Link from 'next/link';

export default function HomeClient() {
	const { t } = useTranslation();

	return (
		<main role="main" aria-label={t('accessibility.home_page')}>
			<ScreenReaderAnnouncement message={t('accessibility.home')} priority="polite" />
			<div className="fixed inset-0 -z-10 bg-background" />
			<div className="space-y-16 text-center">
				<HeroSection />
				<div className="space-y-6 pb-16">
					<h2 className="text-3xl font-bold">
						<Translate text="home.ready_to_start" />
					</h2>
					<p className="text-muted-foreground text-lg max-w-2xl mx-auto">
						<Translate text="home.join_thousands" />
					</p>
					<Link href="/collections">
						<Button
							size="lg"
							className="bg-primary hover:bg-primary/90 transition-all duration-300 text-lg px-12 py-6 h-auto"
						>
							<BookOpen className="size-5 mr-2" />
							<Translate text="home.start_learning_now" />
							<ArrowRight className="size-5 ml-2" />
						</Button>
					</Link>
				</div>
			</div>
		</main>
	);
}
