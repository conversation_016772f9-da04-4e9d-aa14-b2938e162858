'use client';

import { ErrorBoundary } from '@/components/error/error-boundary';
import { DataFallback, ImageFallback } from '@/components/fallback';
import { ErrorList } from '@/components/ui';
import { useError, useErrorHandler } from '@/contexts/error-context';
import { COMMON_FEATURES, useGracefulDegradation } from '@/hooks/use-graceful-degradation';

import { apiClient } from '@/lib/api-interceptor';
import {
	AppError,
	ErrorCategory,
	ErrorSeverity,
	NetworkError,
	ServerError,
	ValidationError,
} from '@/lib/error-handling';
import { useRetry } from '@/lib/retry';
import { useState } from 'react';

export default function ErrorTestPage() {
	const { errorState, addError, clearErrors } = useError();
	const { handleError } = useErrorHandler('ErrorTestPage');
	const [isLoading, setIsLoading] = useState(false);
	const [testData, setTestData] = useState<string | null>(null);

	// Offline functionality removed

	// Test graceful degradation
	const { currentLevel, availableFeatures, isChecking } = useGracefulDegradation({
		features: [COMMON_FEATURES.api, COMMON_FEATURES.storage, COMMON_FEATURES.notifications],
	});

	// Test retry functionality
	const { execute: executeWithRetry, isRetrying } = useRetry(
		async () => {
			// Simulate API call that might fail
			if (Math.random() < 0.7) {
				throw new NetworkError('Simulated network failure');
			}
			return 'Success!';
		},
		{ enabled: true, maxAttempts: 3 }
	);

	// Test different error types
	const triggerValidationError = () => {
		const error = new ValidationError('Please fill in all required fields');
		addError(error, 'ErrorTestPage', { action: 'validation_test' });
	};

	const triggerNetworkError = () => {
		const error = new NetworkError('Failed to connect to server');
		addError(error, 'ErrorTestPage', { action: 'network_test' });
	};

	const triggerServerError = () => {
		const error = new ServerError('Internal server error occurred');
		addError(error, 'ErrorTestPage', { action: 'server_test' });
	};

	const triggerCriticalError = () => {
		const error = new AppError(
			'Critical system failure',
			'CRITICAL_ERROR',
			500,
			ErrorSeverity.CRITICAL,
			ErrorCategory.SERVER
		);
		addError(error, 'ErrorTestPage', { action: 'critical_test' });
	};

	// Test API client with retry
	const testApiCall = async () => {
		setIsLoading(true);
		try {
			// This will fail since the endpoint doesn't exist, but will demonstrate retry logic
			const result = await apiClient.get('/api/test-endpoint');
			setTestData(result);
		} catch (error) {
			handleError(error, 'api_test');
		} finally {
			setIsLoading(false);
		}
	};

	// Test retry mechanism
	const testRetryMechanism = async () => {
		try {
			const result = await executeWithRetry();
			setTestData(result);
		} catch (error) {
			handleError(error, 'retry_test');
		}
	};

	// Component that throws an error for testing error boundary
	const ErrorThrowingComponent = () => {
		const [shouldThrow, setShouldThrow] = useState(false);

		if (shouldThrow) {
			throw new Error('Test error from component');
		}

		return (
			<div className="p-4 border rounded">
				<h3 className="font-semibold mb-2">Error Boundary Test</h3>
				<button
					onClick={() => setShouldThrow(true)}
					className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
				>
					Throw Error
				</button>
			</div>
		);
	};

	return (
		<div className="max-w-4xl mx-auto p-6 space-y-8">
			<div className="text-center">
				<h1 className="text-3xl font-bold mb-4">Error Handling System Test</h1>
				<p className="text-gray-600 dark:text-gray-300">
					Test various error handling features and components
				</p>
			</div>

			{/* Network Status - Removed */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">Network Status (Removed)</h2>
				<div className="space-y-2">
					<p className="text-gray-500 dark:text-gray-400">
						Network detection functionality has been removed from the application.
					</p>
				</div>
			</div>

			{/* Graceful Degradation */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">Graceful Degradation</h2>
				<div className="space-y-2">
					<p>
						Current Level: <span className="font-mono">{currentLevel.level}</span>
					</p>
					<p>Description: {currentLevel.description}</p>
					<p>Available Features: {availableFeatures.join(', ')}</p>
					<p>Checking: {isChecking ? 'Yes' : 'No'}</p>
				</div>
			</div>

			{/* Error Triggers */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">Error Triggers</h2>
				<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
					<button
						onClick={triggerValidationError}
						className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
					>
						Validation Error
					</button>
					<button
						onClick={triggerNetworkError}
						className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
					>
						Network Error
					</button>
					<button
						onClick={triggerServerError}
						className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
					>
						Server Error
					</button>
					<button
						onClick={triggerCriticalError}
						className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
					>
						Critical Error
					</button>
				</div>
			</div>

			{/* API Testing */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">API & Retry Testing</h2>
				<div className="space-y-4">
					<div className="flex space-x-4">
						<button
							onClick={testApiCall}
							disabled={isLoading}
							className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
						>
							{isLoading ? 'Testing...' : 'Test API Call'}
						</button>
						<button
							onClick={testRetryMechanism}
							disabled={isRetrying}
							className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
						>
							{isRetrying ? 'Retrying...' : 'Test Retry Mechanism'}
						</button>
					</div>
					{testData && (
						<div className="p-3 bg-green-100 dark:bg-green-900/20 rounded">
							<p className="text-green-800 dark:text-green-200">Result: {testData}</p>
						</div>
					)}
				</div>
			</div>

			{/* Error Boundary Test */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">Error Boundary Test</h2>
				<ErrorBoundary level="section" name="TestErrorBoundary">
					<ErrorThrowingComponent />
				</ErrorBoundary>
			</div>

			{/* Fallback Components Test */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">Fallback Components</h2>
				<div className="space-y-4">
					<div>
						<h3 className="font-semibold mb-2">Image Fallback</h3>
						<ImageFallback
							src="https://invalid-url.com/image.jpg"
							alt="Test image"
							className="w-32 h-32 object-cover"
						/>
					</div>
					<div>
						<h3 className="font-semibold mb-2">Data Fallback (Empty State)</h3>
						<DataFallback
							isEmpty={true}
							emptyMessage="No data available"
							className="h-32"
						>
							<p>This content won&apos;t show</p>
						</DataFallback>
					</div>
				</div>
			</div>

			{/* Current Errors Display */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<div className="flex justify-between items-center mb-4">
					<h2 className="text-xl font-semibold">Current Errors</h2>
					<button
						onClick={clearErrors}
						className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
					>
						Clear All
					</button>
				</div>
				{errorState.errors.length > 0 ? (
					<ErrorList
						errors={errorState.errors}
						onDismiss={(errorId) => {
							// Remove specific error
							console.log('Dismiss error:', errorId);
						}}
						onRetry={(error) => {
							console.log('Retry error:', error);
						}}
					/>
				) : (
					<p className="text-gray-500 dark:text-gray-400">No errors currently</p>
				)}
			</div>

			{/* Network Fallback Wrapper - Removed */}
			<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
				<h2 className="text-xl font-semibold mb-4">Network-Dependent Content</h2>
				<p>
					This content was previously wrapped in a NetworkFallback component (now
					removed).
				</p>
			</div>
		</div>
	);
}
