'use client';

import React, { createContext, useContext, useState, ReactNode, useCallback, useRef } from 'react';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface GuidanceConfig {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	defaultOpen?: boolean;
}

interface GuidanceContextType {
	isOpen: boolean;
	config: GuidanceConfig | null;
	hasUserInteracted: boolean;
	showGuidance: (config: GuidanceConfig) => void;
	hideGuidance: () => void;
	closeGuidance: () => void;
	toggleGuidance: () => void;
	markUserInteraction: () => void;
}

const GuidanceContext = createContext<GuidanceContextType | undefined>(undefined);

export function GuidanceProvider({ children }: { children: ReactNode }) {
	const [isOpen, setIsOpen] = useState(false);
	const [config, setConfig] = useState<GuidanceConfig | null>(null);
	const [hasUserInteracted, setHasUserInteracted] = useState(false);
	const hasUserInteractedRef = useRef(hasUserInteracted);

	// Keep ref in sync with state
	hasUserInteractedRef.current = hasUserInteracted;

	const showGuidance = useCallback(
		(newConfig: GuidanceConfig) => {
			setConfig(newConfig);
			// Only auto-open if user hasn't interacted and defaultOpen is true
			if (!hasUserInteractedRef.current) {
				setIsOpen(newConfig.defaultOpen || false);
			}
		},
		[] // Use ref to avoid dependency on hasUserInteracted state
	);

	const hideGuidance = useCallback(() => {
		setIsOpen(false);
		setConfig(null); // Clear the config when hiding guidance
		setHasUserInteracted(true);
	}, []);

	const closeGuidance = useCallback(() => {
		setIsOpen(false);
		setHasUserInteracted(true);
		// Keep the config so the button remains visible
	}, []);

	const toggleGuidance = useCallback(() => {
		if (config) {
			setIsOpen(!isOpen);
			setHasUserInteracted(true);
		}
	}, [config, isOpen]);

	const markUserInteraction = useCallback(() => {
		setHasUserInteracted(true);
	}, []);

	return (
		<GuidanceContext.Provider
			value={{
				isOpen,
				config,
				hasUserInteracted,
				showGuidance,
				hideGuidance,
				closeGuidance,
				toggleGuidance,
				markUserInteraction,
			}}
		>
			{children}
		</GuidanceContext.Provider>
	);
}

export function useGuidance() {
	const context = useContext(GuidanceContext);
	if (context === undefined) {
		throw new Error('useGuidance must be used within a GuidanceProvider');
	}
	return context;
}
