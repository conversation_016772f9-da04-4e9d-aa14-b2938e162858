'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Role } from '@prisma/client';

// ============================================================================
// TYPES
// ============================================================================

export interface AdminUser {
	id: string;
	username: string;
	role: Role;
	disabled: boolean;
}

export interface AdminAuthContextType {
	user: AdminUser | null;
	isLoading: boolean;
	isAuthenticated: boolean;
	isAdmin: boolean;
	login: (username: string, password: string) => Promise<void>;
	logout: () => Promise<void>;
	checkAuth: () => Promise<void>;
}

// ============================================================================
// CONTEXT
// ============================================================================

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

// ============================================================================
// PROVIDER
// ============================================================================

interface AdminAuthProviderProps {
	children: ReactNode;
}

export function AdminAuthProvider({ children }: AdminAuthProviderProps) {
	const [user, setUser] = useState<AdminUser | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const router = useRouter();

	// Computed properties
	const isAuthenticated = !!user;
	const isAdmin = user?.role === Role.ADMIN && !user?.disabled;

	// Check authentication status
	const checkAuth = async () => {
		try {
			setIsLoading(true);
			
			const response = await fetch('/api/user/current', {
				credentials: 'include',
			});

			if (response.ok) {
				const userData = await response.json();
				
				// Verify user is admin and not disabled
				if (userData.role === Role.ADMIN && !userData.disabled) {
					setUser({
						id: userData.id,
						username: userData.username || '',
						role: userData.role,
						disabled: userData.disabled || false,
					});
				} else {
					// User exists but is not admin or is disabled
					setUser(null);
					throw new Error('Admin access required');
				}
			} else {
				// Not authenticated
				setUser(null);
				throw new Error('Not authenticated');
			}
		} catch (error) {
			console.error('Admin auth check failed:', error);
			setUser(null);
			// Don't redirect here - let the guard components handle it
		} finally {
			setIsLoading(false);
		}
	};

	// Login function
	const login = async (username: string, password: string) => {
		try {
			const response = await fetch('/api/admin/auth', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ username, password }),
				credentials: 'include',
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || 'Login failed');
			}

			if (data.success && data.data?.user) {
				const userData = data.data.user;
				setUser({
					id: userData.id,
					username: userData.username,
					role: userData.role,
					disabled: false, // Admin login already checks this
				});
			} else {
				throw new Error('Invalid response from server');
			}
		} catch (error) {
			console.error('Admin login failed:', error);
			throw error;
		}
	};

	// Logout function
	const logout = async () => {
		try {
			await fetch('/api/admin/auth', {
				method: 'DELETE',
				credentials: 'include',
			});
		} catch (error) {
			console.error('Admin logout error:', error);
		} finally {
			setUser(null);
			router.push('/admin/login');
		}
	};

	// Check auth on mount
	useEffect(() => {
		checkAuth();
	}, []);

	const value: AdminAuthContextType = {
		user,
		isLoading,
		isAuthenticated,
		isAdmin,
		login,
		logout,
		checkAuth,
	};

	return <AdminAuthContext.Provider value={value}>{children}</AdminAuthContext.Provider>;
}

// ============================================================================
// HOOK
// ============================================================================

export function useAdminAuth(): AdminAuthContextType {
	const context = useContext(AdminAuthContext);
	if (context === undefined) {
		throw new Error('useAdminAuth must be used within an AdminAuthProvider');
	}
	return context;
}

// ============================================================================
// EXPORTS
// ============================================================================

export default AdminAuthProvider;
