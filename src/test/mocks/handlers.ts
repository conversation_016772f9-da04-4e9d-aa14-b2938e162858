import { http, HttpResponse } from 'msw';

export const handlers = [
	// Auth endpoints
	http.post('/api/auth/login', () => {
		return HttpResponse.json({
			user: {
				id: 'test-user-id',
				username: 'testuser',
				provider: 'USERNAME_PASSWORD',
			},
			token: 'test-jwt-token',
		});
	}),

	http.get('/api/auth/me', () => {
		return HttpResponse.json({
			id: 'test-user-id',
			username: 'testuser',
			provider: 'USERNAME_PASSWORD',
		});
	}),

	http.post('/api/auth/logout', () => {
		return HttpResponse.json({ success: true });
	}),

	// Collections endpoints
	http.get('/api/collections', () => {
		return HttpResponse.json({
			collections: [
				{
					id: 'test-collection-id',
					name: 'Test Collection',
					target_language: 'EN',
					source_language: 'VI',
					word_ids: ['word-1', 'word-2'],
					paragraph_ids: [],
					keyword_ids: [],
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString(),
				},
			],
		});
	}),

	http.post('/api/collections', async ({ request }) => {
		const body = await request.json();
		return HttpResponse.json({
			id: 'new-collection-id',
			name: (body as any).name,
			target_language: (body as any).target_language,
			source_language: (body as any).source_language,
			word_ids: [],
			paragraph_ids: [],
			keyword_ids: [],
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
		});
	}),

	// Words endpoints
	http.get('/api/words/search', ({ request }) => {
		const url = new URL(request.url);
		const term = url.searchParams.get('term');

		return HttpResponse.json({
			words: [
				{
					id: 'word-1',
					term: term || 'test',
					language: 'EN',
					definitions: [
						{
							id: 'def-1',
							pos: ['NOUN'],
							ipa: '/test/',
							explains: [
								{
									id: 'explain-1',
									EN: 'Test explanation',
									VI: 'Giải thích test',
								},
							],
							examples: [
								{
									id: 'example-1',
									EN: 'Test example',
									VI: 'Ví dụ test',
								},
							],
						},
					],
				},
			],
		});
	}),

	// LLM endpoints
	http.post('/api/llm/generate-words', () => {
		return HttpResponse.json({
			words: [
				{
					term: 'algorithm',
					language: 'EN',
					definitions: [
						{
							pos: ['NOUN'],
							ipa: '/ˈælɡərɪðəm/',
							explains: [
								{
									EN: 'A process or set of rules to be followed in calculations',
									VI: 'Một quy trình hoặc tập hợp các quy tắc được tuân theo trong tính toán',
								},
							],
							examples: [
								{
									EN: 'The algorithm solved the problem efficiently',
									VI: 'Thuật toán đã giải quyết vấn đề một cách hiệu quả',
								},
							],
						},
					],
				},
			],
		});
	}),

	http.post('/api/llm/generate-paragraph', () => {
		return HttpResponse.json({
			paragraph: {
				content: 'This is a test paragraph about technology and computers.',
				difficulty: 'INTERMEDIATE',
				language: 'EN',
				length: 'MEDIUM',
			},
		});
	}),

	// Grammar Practice API
	http.post('/api/llm/generate-grammar-practice', () => {
		return HttpResponse.json([
			{
				paragraphWithErrors:
					'This is a test paragraph with some grammer mistakes and wrong words.',
				correctedParagraph:
					'This is a test paragraph with some grammar mistakes and wrong words.',
				allErrors: [
					{
						errorText: 'grammer',
						correctedText: 'grammar',
						errorType: 'spelling',
						explanation: {
							source_language: 'Lỗi chính tả: "grammer" phải là "grammar"',
							target_language: 'Spelling error: "grammer" should be "grammar"',
						},
					},
				],
			},
		]);
	}),

	// OpenAI API mock
	http.post('https://api.openai.com/v1/chat/completions', () => {
		return HttpResponse.json({
			choices: [
				{
					message: {
						content: JSON.stringify({
							words: [
								{
									term: 'technology',
									language: 'EN',
									definitions: [
										{
											pos: ['NOUN'],
											ipa: '/tekˈnɒlədʒi/',
											explains: [
												{
													EN: 'The application of scientific knowledge',
													VI: 'Việc ứng dụng kiến thức khoa học',
												},
											],
											examples: [
												{
													EN: 'Technology has changed our lives',
													VI: 'Công nghệ đã thay đổi cuộc sống của chúng ta',
												},
											],
										},
									],
								},
							],
						}),
					},
				},
			],
			usage: {
				prompt_tokens: 100,
				completion_tokens: 200,
				total_tokens: 300,
			},
		});
	}),
];
