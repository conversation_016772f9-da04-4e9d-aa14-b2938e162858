import { FullConfig } from '@playwright/test';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Global teardown for admin dashboard tests
 * 
 * This teardown:
 * 1. Cleans up test data
 * 2. Optionally stops database containers
 * 3. Generates test reports
 * 4. Provides cleanup summary
 */
async function globalTeardown(config: FullConfig) {
	console.log('🧹 Starting admin dashboard test teardown...');
	
	try {
		// 1. Clean up test data (optional - keep for debugging)
		console.log('📊 Cleaning up test data...');
		
		// Note: We might want to keep test data for debugging
		// Uncomment the following if you want to reset the database
		// await execAsync('yarn p:m:r');
		
		// 2. Generate test summary
		console.log('📈 Generating test summary...');
		
		// The test results will be available in the configured output directories
		console.log('');
		console.log('📋 Test Results Available:');
		console.log('  • HTML Report: playwright-report-admin/index.html');
		console.log('  • JSON Report: test-results-admin.json');
		console.log('  • JUnit Report: test-results-admin.xml');
		console.log('  • Screenshots: test-results-admin/ (on failures)');
		console.log('  • Videos: test-results-admin/ (on failures)');
		console.log('');
		
		// 3. Cleanup recommendations
		console.log('🔧 Post-Test Cleanup Options:');
		console.log('  • Keep database running: Data preserved for debugging');
		console.log('  • Stop database: Run "docker-compose down"');
		console.log('  • Reset database: Run "yarn p:m:r"');
		console.log('  • View reports: Open playwright-report-admin/index.html');
		console.log('');
		
		// 4. Optional: Stop database containers (commented out by default)
		// Uncomment if you want to automatically stop containers after tests
		/*
		console.log('🛑 Stopping database containers...');
		await execAsync('docker-compose down');
		console.log('✅ Database containers stopped');
		*/
		
		console.log('✅ Admin dashboard test teardown completed successfully!');
		
	} catch (error) {
		console.error('❌ Admin dashboard test teardown failed:', error);
		console.error('');
		console.error('🔧 Manual Cleanup:');
		console.error('  1. Stop containers: docker-compose down');
		console.error('  2. Clean test data: yarn p:m:r');
		console.error('  3. Check for hanging processes');
		console.error('');
		
		// Don't throw error in teardown to avoid masking test results
		console.warn('⚠️ Teardown failed but continuing to preserve test results');
	}
}

export default globalTeardown;
