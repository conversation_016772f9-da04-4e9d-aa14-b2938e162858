-   **Phân tích tự động mã nguồn (Static Code Analysis):**
    -   Quét mã để phát hiện lỗ hổng bảo mật, lỗi cú pháp, vi phạm coding style, phức tạp dư thừa.
    -   Hỗ trợ trên 40 ngôn ngữ lập trình như Python, Java, C#, PHP, JavaScript, v.v.
-   **Bảo mật mã nguồn:**
    -   SAST (Static Application Security Testing)
    -   SCA (Software Composition Analysis, kiểm tra thư viện phụ thuộc)
    -   Secret scanning (phát hiện thông tin nhạy cảm trong code như API key)
    -   DAST (Dynamic Application Security Testing)
    -   Penetration testing tự động
    -   IaC (Infrastructure-as-code) scan: Phát hiện cấu hình hạ tầng sai.
-   **AI Guardrails:**
    -   Phát hiện và khắc phục các rủi ro b<PERSON><PERSON> mậ<PERSON>, chất lượng trong mã nguồn do AI sinh ra.
    -   G<PERSON><PERSON> diện tích hợp trong IDE (VS Code, Cursor, Copilot).
-   **Quản lý chất lượng & kỹ thuật:**
    -   Đo code coverage (tỷ lệ bao phủ kiểm thử), code complexity (độ phức tạp), code duplication (đoạn mã lặp), technical debt (nợ kỹ thuật).
    -   Gán mục tiêu cải thiện chất lượng theo từng dự án.
    -   Customizable Quality Gates: Thiết lập các tiêu chuẩn chất lượng để chặn merge pull request khi chưa đạt yêu cầu.
-   **Tích hợp liền mạch:**
    -   Kết nối với Github, GitLab, Bitbucket, Slack, JIRA…
    -   Phản hồi thời gian thực trên pull request, commit, dashboard.
-   **Quản lý và báo cáo tập trung:**
    -   Dashboard đơn giản, báo cáo tự động về tình trạng bảo mật, chất lượng code.
    -   Tích hợp hệ thống chấm điểm, thống kê lịch sử, xuất dữ liệu.
-   **Không cần pipeline riêng biệt (pipeline-less):**
    -   Quét mã ngoài CI/CD pipelines, giảm cấu hình phức tạp, tiết kiệm chi phí vận hành.

| Usecase                         | Mô tả ngắn                                                   |
| ------------------------------- | ------------------------------------------------------------ |
| Automated Code Review           | Tự động hóa việc kiểm tra code, giảm thời gian review        |
| Pull Request Gating             | Chặn merge code khi chưa đạt tiêu chí bảo mật/chất lượng     |
| Phát hiện lỗi bảo mật & báo cáo | Tự động phát hiện, cảnh báo sớm các lỗ hổng và sai sót       |
| Quản lý nợ kỹ thuật             | Đo lường, quản lý technical debt toàn đội                    |
| Theo dõi code coverage          | Đánh giá, nâng cao tỷ lệ test coverage mọi lúc               |
| Chuẩn hóa quy tắc code          | Đảm bảo tuân thủ thống nhất coding convention tổ chức        |
| Tích hợp đánh giá AI code       | Kiểm soát AI-generated code sinh ra với Guardrails           |
| Báo cáo & theo dõi tiến độ      | Quản lý chất lượng lâu dài, thống kê lịch sử cải tiến        |
| Đào tạo/dev hướng dẫn           | Gợi ý những lần mắc lỗi phổ biến, góp phần nâng cao tay nghề |
