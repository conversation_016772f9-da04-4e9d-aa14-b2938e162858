# VOCAB PROJECT - ARCHITECTURE OVERVIEW

## 📋 PROJECT OVERVIEW

**Vocab** là một ứng dụng học từ vựng AI-powered được xây dựng với Next.js 15, tập trung vào việc mở rộng từ vựng thông qua spaced repetition, nội dung được tạo bởi AI và các bộ sưu tập học tập có tổ chức.

### Core Features

-   **Collection Management**: Tổ chức từ vựng theo chủ đề
-   **AI-Powered Content**: Tích hợp LLM để tạo từ vựng, đoạn văn, câu hỏi
-   **Spaced Repetition**: Hệ thống ôn tập thông minh
-   **Multi-language Support**: Hỗ trợ tiếng Anh và tiếng Việt
-   **Practice Modes**: Đa dạng hình thức luyện tập

## 🏗️ TECHNOLOGY STACK

### Frontend

-   **Framework**: Next.js 15 với App Router
-   **Language**: TypeScript (strict mode)
-   **Styling**: Tailwind CSS v4 với custom animations
-   **UI Components**: Radix UI primitives với design system tùy chỉnh
-   **State Management**: React Context API + Custom hooks
-   **Internationalization**: i18next với next-i18next
-   **Animations**: Framer Motion
-   **Icons**: Lucide React
-   **Package Manager**: Yarn v4.9.2 (KHÔNG sử dụng npm)
-   **Notifications**: Sonner toast system

### Backend

-   **Runtime**: Node.js với Next.js API routes
-   **Database**: PostgreSQL với Prisma ORM
-   **Authentication**: JWT với multiple providers (Telegram, Google, Username/Password)
-   **AI Integration**: OpenAI GPT-4o-mini + Google Gemini (via Genkit)
-   **Caching**: Node-cache với file-based persistence + Redis (ioredis)
-   **Security**: Security headers, CSRF protection, rate limiting

### Infrastructure

-   **Deployment**: Vercel với automatic CI/CD
-   **Development**: Docker Compose cho local database
-   **Environment**: Environment-based configuration
-   **PWA**: Service worker và manifest cho offline support
-   **Testing**: Jest + Vitest + Playwright + MSW

## 🏛️ ARCHITECTURE PATTERNS

### Clean Architecture Implementation

Dự án áp dụng Domain-Driven Design (DDD) với kiến trúc phân lớp rõ ràng:

```
src/
├── app/                     # Next.js App Router - Presentation Layer
├── backend/                 # Server-side Business Logic
│   ├── services/           # Business Logic Layer (21 services)
│   ├── repositories/       # Data Access Layer (9 repositories)
│   ├── middleware/         # Server Middleware
│   ├── schemas/            # MongoDB Schemas & Validation
│   ├── errors/             # Custom Error Classes
│   ├── utils/              # Backend Utilities
│   ├── config/             # Backend Configuration
│   └── wire.ts            # Dependency Injection Container
├── components/             # UI Components Library
├── contexts/              # React Context Providers (11 contexts)
├── hooks/                 # Custom React Hooks (9 hooks)
├── lib/                   # Shared Utilities & Integrations
├── models/                # Domain Models (5 models)
├── types/                 # TypeScript Type Definitions
├── config/                # Configuration Management
├── constants/             # Application Constants
├── providers/             # React Providers
├── styles/                # Custom CSS Styles
└── test/                  # Testing Infrastructure
```

### Key Design Principles

1. **Separation of Concerns**: Tách biệt rõ ràng giữa API, Service và Repository layers
2. **Dependency Injection**: Centralized service wiring trong `wire.ts`
3. **Type Safety**: Sử dụng TypeScript toàn diện với Prisma-generated types
4. **Security First**: Multiple security layers với validation, rate limiting, CSRF protection
5. **Performance**: Caching strategies, optimized LLM usage, skeleton loading states

## 🔧 BACKEND ARCHITECTURE

### Layer Organization

#### 1. Service Layer (`src/backend/services`)

-   **Responsibility**: Business logic, orchestration
-   **Pattern**: Service interfaces với implementation classes
-   **Key Services**:
    -   `AuthService`: Authentication và user session management
    -   `CollectionService`: Collection CRUD và word/term management
    -   `WordService`: Word search, retrieval, vocabulary operations
    -   `LLMService`: OpenAI GPT-4o-mini integration cho AI features
    -   `GeminiService`: Google Gemini integration via Genkit
    -   `CacheService`: Multi-tier caching operations
    -   `RedisCacheService`: Redis-based caching
    -   `SemanticCacheService`: Semantic similarity caching
    -   `ServerCacheService`: Server-side caching
    -   `CacheFactoryService`: Cache strategy selection
    -   `UserService`: User profile và settings management
    -   `KeywordService`: Keyword search và management
    -   `FeedbackService`: User feedback collection
    -   `LastSeenWordService`: Spaced repetition tracking
    -   `CollectionStatsService`: Collection analytics
    -   `TokenMonitorService`: AI token usage monitoring
    -   `BatchProcessorService`: Batch processing operations
    -   `ModelSelectorService`: AI model selection
    -   `PromptOptimizerService`: Prompt optimization

#### 2. Repository Layer (`src/backend/repositories`)

-   **Responsibility**: Data access, Prisma operations
-   **Pattern**: Repository pattern với base repository
-   **Features**: Support cả PostgreSQL và MongoDB
-   **Base Repository**: Tất cả repositories extend `BaseRepositoryImpl<T>`
-   **Methods**: `findById`, `findOne`, `find`, `create`, `update`, `delete`
-   **Dual Database**: Có implementation riêng cho PostgreSQL và MongoDB

#### 3. Dependency Injection (`src/backend/wire.ts`)

-   **Pattern**: Factory functions với lazy initialization
-   **Benefits**: Loose coupling, testability, service lifecycle management
-   **Usage**: `getCollectionService()`, `getUserRepository()`, etc.
-   **Singleton Pattern**: Tất cả services và repositories là singletons
-   **Database Switching**: Tự động chọn repository implementation dựa trên feature flags

### Database Strategy

-   **Primary**: PostgreSQL với Prisma ORM
-   **Schema**: Comprehensive schema với User, Collection, Word, Paragraph models
-   **Enums**: Language (EN/VI), Provider (Telegram/Google/Username), PartsOfSpeech, Difficulty, Length
-   **Relations**: Complex relationships giữa User → Collections → Words → Paragraphs
-   **Migrations**: Prisma migrations cho schema evolution
-   **Health Checks**: Built-in health check endpoints

## 🎨 FRONTEND ARCHITECTURE

### Component Organization

#### 1. App Router Structure (`src/app`)

-   **Layout**: Root layout với provider hierarchy
-   **Pages**: Client/server component separation
-   **Routing**: File-based routing với dynamic routes
-   **Structure**:
    ```
    src/app/
    ├── layout.tsx              # Root layout với PWA support
    ├── page.tsx                # Home page với animations
    ├── globals.css             # Global styles
    ├── components/             # App-specific components
    └── collections/            # Collections feature pages
        ├── [id]/              # Dynamic collection routes
        │   ├── page.tsx       # Collection detail page
        │   ├── paragraph/     # Paragraph practice
        │   └── components/    # Collection-specific components
        └── page.tsx           # Collections listing page
    ```

#### 2. Component Layer (`src/components`)

-   **UI Components**: Reusable primitives trong `src/components/ui`
    -   50+ UI components: Button, Card, Dialog, Form, Input, etc.
    -   Accessibility-first design với ARIA support
    -   Consistent styling với Tailwind CSS
    -   Export pattern: Tất cả components export qua `index.ts`
-   **Feature Components**: Domain-specific components
    -   Home components: HeroSection, CollectionsSection, etc.
    -   Auth components: AuthGuard, LoginForm, etc.
    -   Floating UI: FloatingUIManager, SimpleFloatingProvider

#### 3. State Management

-   **Global State**: React Context providers
    -   AuthContext: User authentication state
    -   TranslationContext: Language và translation state
    -   LoadingContext: Loading states với scoped loading
    -   ToastContext: Toast notifications
    -   ThemeContext: Dark/light theme
-   **Local State**: Custom hooks
    -   `useCollections`: Collection management
    -   `useOffline`: Offline detection
    -   `useGracefulDegradation`: Feature degradation
-   **Server State**: SWR pattern với API integration
    -   Caching strategies
    -   Optimistic updates
    -   Error handling

### Internationalization System

#### Translation Architecture

-   **Translation Files**: Organized theo domain trong `src/contexts/translations/`
-   **Translation Keys**: Structured keys như `nav.home`, `collections.create`
-   **Language Support**: English (EN) và Vietnamese (VI)
-   **Type Safety**: TypeScript types cho translation keys

#### Translation Structure

```
src/contexts/translations/
├── index.ts              # Main export file
├── translation-dict.ts   # Type definitions
├── nav.ts               # Navigation translations
├── home.ts              # Home page translations
├── collections.ts       # Collections translations
├── ui.ts                # UI component translations
└── errors.ts            # Error message translations
```

### Styling System

#### Tailwind CSS Configuration

-   **Version**: Tailwind CSS v4
-   **Design System**: Custom design tokens với CSS variables
-   **Theme**: Support dark/light mode
-   **Animations**: Custom animations với `tw-animate-css`
-   **Components**: Radix UI primitives với custom styling

#### CSS Architecture

```
src/app/globals.css
├── @import 'tailwindcss'
├── @import 'tw-animate-css'
├── Custom CSS variables
├── Base styles
├── Component styles
└── Utility classes
```

## 🔐 SECURITY IMPLEMENTATION

### Multi-Layer Security

#### 1. Middleware Level (`src/middleware.ts`)

-   **Authentication**: JWT token verification cho tất cả API routes
-   **Public Endpoints**: `/api/auth/*` không cần authentication
-   **Rate Limiting**: IP-based request throttling
-   **Security Headers**: X-Content-Type-Options, X-Frame-Options, X-XSS-Protection
-   **CORS**: Configurable origin validation

#### 2. API Level (`src/lib/api-error-middleware.ts`)

-   **Input Validation**: Zod schema validation cho tất cả API endpoints
-   **Error Handling**: Structured error responses với request tracing
-   **Authentication**: Protected route middleware
-   **Middleware Functions**:
    -   `withErrorHandling`: Error boundary cho API routes
    -   `withValidation`: Schema validation
    -   `withRateLimit`: Rate limiting
    -   `withAuth`: Authentication checks
    -   `withCors`: CORS handling

#### 3. Authentication Flow

-   **JWT Tokens**: HttpOnly cookies với secure flags
-   **Multiple Providers**:
    -   Telegram Bot authentication
    -   Google OAuth (feature flag)
    -   Username/Password authentication
-   **Token Management**:
    -   Automatic refresh
    -   Secure storage trong HttpOnly cookies
    -   Environment-based expiration

#### 4. Error Handling System

-   **Comprehensive Error Management**: `src/lib/error-handling.ts`
-   **Error Types**: AppError, ValidationError, NetworkError, etc.
-   **Error Logging**: Centralized error logger với context
-   **Error Boundaries**: React error boundaries cho UI
-   **Graceful Degradation**: Feature degradation khi có lỗi

## ⚡ PERFORMANCE OPTIMIZATION

### Caching Strategy

#### 1. LLM Response Caching (`src/backend/services/cache.service.ts`)

-   **File-based Caching**: TTL caching với different TTLs:
    -   Vocabulary: 7 days (604800s)
    -   Word Details: 7 days (604800s)
    -   Paragraphs: 3 days (259200s)
    -   Questions: 3 days (259200s)
    -   Evaluations: 30 days (2592000s)
    -   Grammar Practice: 1 day (86400s)
-   **Semantic Caching**: Similar request detection (feature flag)
-   **Cache Keys**: Structured cache keys với semantic indexing
-   **Cache Management**: Automatic cleanup, TTL management

#### 2. Database Caching

-   **In-memory**: Node-cache cho frequently accessed data
-   **Query Optimization**: Prisma query optimization với includes
-   **Connection Pooling**: Database connection management
-   **Dual Database**: Caching strategy cho cả PostgreSQL và MongoDB

#### 3. Frontend Performance

-   **Loading States**: Comprehensive skeleton components:
    -   `ListSkeleton`, `HomeSkeleton`, `CollectionsSkeleton`
    -   `PageSkeleton`, `StatsSkeleton`, `PracticeSessionSkeleton`
-   **Code Splitting**: Dynamic imports cho heavy components
-   **Image Optimization**: Next.js Image component với optimization
-   **Bundle Optimization**: Turbopack cho fast development builds

### LLM Optimization System

#### 1. Model Selection (`src/backend/services/llm.service.ts`)

-   **Adaptive Model Selection**: Chọn model dựa trên complexity
-   **Cost Optimization**: Balance giữa cost và quality
-   **Quality Threshold**: Minimum quality requirements
-   **Latency Threshold**: Maximum response time limits

#### 2. Token Management

-   **Budget Limits**: Daily và monthly token budgets
-   **Cost Alerts**: Threshold-based cost alerting
-   **Usage Tracking**: Comprehensive token usage monitoring
-   **Estimation**: Token estimation trước khi gọi API

#### 3. Prompt Optimization

-   **Compression Techniques**: Reduce prompt size
-   **Template Optimization**: Optimized prompt templates
-   **Batch Processing**: Batch multiple requests
-   **Context Management**: Efficient context handling

#### 4. Monitoring & Analytics

-   **Usage Tracking**: Track token usage, costs, performance
-   **Performance Metrics**: Latency, success rates, quality scores
-   **Error Monitoring**: LLM API errors và fallbacks
-   **Optimization Insights**: Data-driven optimization recommendations

## 🧪 TESTING STRATEGY

### Test Types & Configuration

#### 1. Unit Tests

-   **Jest**: Component và service testing (`jest.config.js`)
-   **Vitest**: Fast unit testing với HMR (`vitest.config.ts`)
-   **Coverage**: 70% threshold cho lines, functions, branches, statements
-   **Test Files**: `*.test.ts`, `*.test.tsx`, `*.spec.ts`, `*.spec.tsx`

#### 2. Integration Tests

-   **API Testing**: API endpoint testing với real database
-   **Service Integration**: Service layer integration tests
-   **Database Integration**: Repository layer testing

#### 3. E2E Tests

-   **Playwright**: Cross-browser E2E testing (`playwright.config.ts`)
-   **Test Browsers**: Chrome, Firefox, Safari, Mobile Chrome
-   **Test Patterns**: User workflows, critical paths
-   **CI Integration**: Automated E2E testing trong GitHub Actions

#### 4. MongoDB Tests

-   **Separate Configuration**: `jest.config.mongodb.js`
-   **In-memory MongoDB**: MongoDB Memory Server cho testing
-   **Isolation**: Separate test suite cho MongoDB integration
-   **Migration Testing**: Test database migration scripts

### Test Environment Setup

-   **Environment Variables**: Test-specific environment configuration
-   **Database Setup**: Separate test databases
-   **Mock Services**: Mock external services (OpenAI, etc.)
-   **Test Data**: Seed data cho testing

## 🚀 DEVELOPMENT WORKFLOWS

### Development Environment Setup

#### 1. Local Database

-   **Docker Compose**: PostgreSQL + MongoDB containers
-   **Commands**: `yarn dup` để start databases
-   **Ports**: PostgreSQL (5432), MongoDB (27017)
-   **Data Persistence**: Docker volumes cho data persistence

#### 2. Development Tools

-   **Hot Reload**: Turbopack cho fast refresh
-   **Type Checking**: Continuous TypeScript validation
-   **Performance**: React Scan integration (`yarn scan`)
-   **Database Tools**: Prisma Studio (`yarn p:s`)

#### 3. Environment Variables

-   **Required**: `DATABASE_URL`, `JWT_SECRET`, `LLM_OPENAI_API_KEY`
-   **Optional**: `TELEGRAM_BOT_TOKEN`, `GOOGLE_CLIENT_ID`
-   **Feature Flags**: `FEATURE_MONGODB_ENABLED`, `FEATURE_GOOGLE_LOGIN`
-   **Configuration**: Copy từ `.env.example` sang `.env.local`

### CI/CD Pipeline

#### 1. GitHub Actions (`.github/workflows/nextjs.yml`)

-   **Triggers**: Push to tags `v*`
-   **Jobs**: Lint → Build → Deploy
-   **Node Version**: 20
-   **Package Manager**: Yarn với caching
-   **Build Optimization**: Next.js build caching

#### 2. Vercel Deployment

-   **Auto Deploy**: Từ main branch
-   **Build Command**: `yarn build` (includes Prisma migration)
-   **Environment**: Production environment variables
-   **Region**: Singapore (sin1) cho optimal performance
-   **Framework**: Next.js với automatic optimization

#### 3. Database Migration

-   **Development**: `yarn p:m` cho dev migrations
-   **Production**: Automatic migration trong build process
-   **Reset**: `yarn p:m:r` cho reset database
-   **MongoDB Migration**: Separate migration scripts

## 📝 CODING CONVENTIONS

### File Naming Conventions

#### 1. Components

-   **React Components**: PascalCase (`UserProfile.tsx`, `CollectionCard.tsx`)
-   **UI Components**: PascalCase (`Button.tsx`, `Dialog.tsx`)
-   **Page Components**: PascalCase (`HomePage.tsx`, `CollectionPage.tsx`)

#### 2. Backend Files

-   **Services**: kebab-case (`collection.service.ts`, `auth.service.ts`)
-   **Repositories**: kebab-case (`user.repository.ts`, `word.repository.ts`)
-   **API Routes**: kebab-case (`auth.api.ts`, `collection.api.ts`)
-   **Middleware**: kebab-case (`auth.middleware.ts`)

#### 3. Other Files

-   **Hooks**: camelCase với `use` prefix (`useCollections.ts`, `useAuth.ts`)
-   **Utilities**: kebab-case (`token.util.ts`, `validation.util.ts`)
-   **Types**: kebab-case (`user.types.ts`, `api.types.ts`)
-   **Constants**: kebab-case (`loading-scopes.ts`, `error-codes.ts`)

## 🔄 DEPLOYMENT & OPERATIONS

### Vercel Configuration

#### 1. Build Configuration (`vercel.json`)

-   **Build Command**: `yarn build` (includes Prisma migration)
-   **Framework**: Next.js với automatic optimization
-   **Output Directory**: `.next`
-   **Install Command**: `yarn install`
-   **Regions**: Singapore (sin1) cho optimal performance

#### 2. Security Headers

-   **X-Content-Type-Options**: `nosniff`
-   **X-Frame-Options**: `DENY`
-   **X-XSS-Protection**: `1; mode=block`

### Environment Management

#### 1. Configuration System (`src/config/config.ts`)

-   **Server Config**: Port, environment settings
-   **Auth Config**: JWT settings, OAuth configurations
-   **LLM Config**: OpenAI settings, optimization parameters
-   **Feature Flags**: Environment-based feature toggles

#### 3. Database Configuration

-   **PostgreSQL**: Primary database với Prisma
-   **MongoDB**: Migration target với feature flags
-   **Dual Database**: Support cả hai databases
-   **Connection Management**: Centralized database manager

### Database Operations

#### 1. Migration Strategy

-   **Development**: `yarn p:m` cho dev migrations
-   **Production**: Automatic migration trong build
-   **MongoDB Migration**: Separate migration scripts
-   **Data Transformation**: Custom migration tools

#### 2. Monitoring & Performance

-   **Connection Monitoring**: Connection pool monitoring
-   **Query Performance**: Prisma query optimization
-   **Error Tracking**: Comprehensive error logging

## 🎯 BEST PRACTICES & GUIDELINES

### Development Guidelines

#### 1. Package Management

-   **Always use Yarn**: KHÔNG bao giờ sử dụng npm
-   **Version**: Yarn v4.9.2
-   **Lock File**: Commit `yarn.lock` file
-   **Scripts**: Sử dụng yarn scripts trong `package.json`

#### 2. Type Safety

-   **TypeScript Strict Mode**: Enabled với comprehensive type checking
-   **Interface Definitions**: Clear props interfaces cho components
-   **Generic Types**: Reusable type patterns
-   **Prisma Types**: Sử dụng generated types cho database models

#### 3. Error Handling

-   **Structured Error Management**: Sử dụng `AppError` classes
-   **Error Boundaries**: React error boundaries cho UI
-   **Logging**: Centralized error logging với context
-   **Graceful Degradation**: Handle errors gracefully

#### 4. Security

-   **Input Validation**: Zod schema validation cho tất cả inputs
-   **Authentication**: JWT tokens với secure storage
-   **Authorization**: Role-based access control
-   **HTTPS**: Always use HTTPS trong production

#### 5. Performance

-   **Caching**: Multi-tier caching strategy
-   **Optimization**: LLM optimization, database query optimization
-   **Monitoring**: Performance monitoring và alerting
-   **Loading States**: Skeleton components cho better UX

#### 6. Testing

-   **Unit Tests**: Write tests cho critical functionality
-   **Integration Tests**: Test API endpoints và services
-   **E2E Tests**: Test user workflows
-   **Coverage**: Maintain 70% test coverage

#### 7. Documentation

-   **Code Comments**: Clear comments cho complex logic
-   **README**: Comprehensive project documentation
-   **API Documentation**: Document API endpoints
-   **Architecture**: Keep architecture docs updated

### Code Quality Standards

#### 1. Code Organization

-   **Clean Architecture**: Separation of concerns
-   **Dependency Injection**: Use wire.ts cho service management
-   **Single Responsibility**: Each class/function has one responsibility
-   **DRY Principle**: Don't repeat yourself

#### 2. Code Style

-   **Linting**: TypeScript strict mode
-   **Formatting**: Consistent code formatting
-   **Naming**: Descriptive variable và function names
-   **Comments**: Meaningful comments, not obvious ones

#### 3. Review Process

-   **Code Reviews**: All code must be reviewed
-   **Pull Requests**: Use PR templates
-   **Testing**: All PRs must include tests
-   **Documentation**: Update docs với code changes

#### 4. Monitoring & Maintenance

-   **Performance Monitoring**: Track performance metrics
-   **Error Tracking**: Monitor và fix errors promptly
-   **Refactoring**: Regular code improvement
-   **Dependencies**: Keep dependencies updated

---

## 📚 GETTING STARTED FOR NEW DEVELOPERS

### 1. Setup Development Environment

```bash
# Clone repository
git clone <repository-url>
cd vocab

# Install dependencies (MUST use Yarn)
yarn install

# Setup environment variables
cp .env.example .env.local
# Edit .env.local với your configurations

# Start local databases
yarn dup

# Run database migrations
yarn p:m

# Start development server
yarn dev
```

### 2. Understanding the Codebase

1. **Start with**: `src/app/layout.tsx` để hiểu provider hierarchy
2. **Explore**: `src/backend/wire.ts` để hiểu dependency injection
3. **Review**: `src/components/ui/index.ts` để see available components
4. **Check**: `src/contexts/translations/` để understand i18n system

### 3. Making Your First Change

1. **Create a branch**: `git checkout -b feature/your-feature`
2. **Follow conventions**: Use proper file naming và patterns
3. **Add tests**: Write tests cho your changes
4. **Update docs**: Update documentation if needed
5. **Create PR**: Follow PR template và guidelines

### 4. Key Files to Understand

-   `src/backend/wire.ts`: Dependency injection container
-   `src/middleware.ts`: Request middleware và authentication
-   `src/app/layout.tsx`: Provider hierarchy và global setup
-   `src/lib/error-handling.ts`: Error management system
-   `src/config/config.ts`: Configuration management
-   `package.json`: Scripts và dependencies

### 5. Common Tasks

-   **Add new API endpoint**: Create trong `src/app/api/`
-   **Add new service**: Create trong `src/backend/services/`
-   **Add new component**: Create trong `src/components/`
-   **Add translations**: Update `src/contexts/translations/`
-   **Run tests**: `yarn test`, `yarn test:e2e`
-   **Database changes**: `yarn p:m` after schema changes

## 📁 DETAILED PROJECT STRUCTURE

### Root Directory

```
vocab/
├── docs/                    # Documentation files (15+ docs)
│   ├── ARCHITECTURE_OVERVIEW.md
│   ├── ERROR_HANDLING_README.md
│   ├── LLM_MODULE_DOCUMENTATION.md
│   ├── TESTING_IMPLEMENTATION_PLAN.md
│   └── ...
├── e2e/                     # End-to-end tests (Playwright)
│   ├── global-setup.ts
│   └── global-teardown.ts
├── prisma/                  # Database schema và migrations
│   ├── migrations/          # Database migration files
│   └── schema.prisma        # Prisma schema definition
├── public/                  # Static assets (PWA icons, manifest)
│   ├── manifest.json
│   ├── sw.js
│   └── *.ico
├── src/                     # Source code (main application)
├── package.json             # Dependencies và scripts (Yarn v4.9.2)
├── next.config.ts           # Next.js configuration
├── tailwind.config.js       # Tailwind CSS v4 configuration
├── tsconfig.json            # TypeScript strict configuration
├── vercel.json              # Vercel deployment configuration
├── docker-compose.yml       # Local PostgreSQL database
├── playwright.config.ts     # E2E testing configuration
├── jest.config.js           # Unit testing configuration
├── vitest.config.ts         # Vitest testing configuration
└── components.json          # Shadcn/ui configuration
```

### Source Code Structure (`src/`)

```
src/
├── app/                     # Next.js App Router (Presentation Layer)
│   ├── layout.tsx           # Root layout với providers
│   ├── page.tsx             # Home page
│   ├── globals.css          # Global styles
│   ├── api/                 # API routes (11 endpoints)
│   │   ├── auth/            # Authentication endpoints
│   │   ├── collections/     # Collection management
│   │   ├── words/           # Word management
│   │   ├── llm/             # AI integration
│   │   ├── keywords/        # Keyword search
│   │   ├── user/            # User management
│   │   ├── feedback/        # User feedback
│   │   ├── admin/           # Admin endpoints
│   │   ├── cache/           # Cache management
│   │   ├── health/          # Health checks
│   │   ├── token-monitor/   # Token monitoring
│   │   └── last-seen-word/  # Spaced repetition
│   ├── collections/         # Collections feature pages
│   │   ├── [id]/            # Dynamic collection routes
│   │   │   ├── page.tsx     # Collection detail
│   │   │   └── paragraph/   # Paragraph practice
│   │   ├── components/      # Collection-specific components
│   │   └── page.tsx         # Collections listing
│   ├── dashboard/           # User dashboard
│   │   └── token-monitoring/ # Token usage dashboard
│   ├── login/               # Authentication pages
│   ├── admin/               # Admin interface
│   │   └── feedback/        # Admin feedback management
│   └── error-test/          # Error testing page
├── backend/                 # Server-side Business Logic
│   ├── services/            # Business Logic Layer (21 services)
│   │   ├── auth.service.ts
│   │   ├── collection.service.ts
│   │   ├── word.service.ts
│   │   ├── llm.service.ts
│   │   ├── gemini.service.ts
│   │   ├── cache.service.ts
│   │   ├── redis-cache.service.ts
│   │   ├── semantic-cache.service.ts
│   │   ├── server-cache.service.ts
│   │   ├── cache-factory.service.ts
│   │   ├── user.service.ts
│   │   ├── keyword.service.ts
│   │   ├── feedback.service.ts
│   │   ├── last-seen-word.service.ts
│   │   ├── collection-stats.service.ts
│   │   ├── token-monitor.service.ts
│   │   ├── batch-processor.service.ts
│   │   ├── model-selector.service.ts
│   │   └── prompt-optimizer.service.ts
│   ├── repositories/        # Data Access Layer (9 repositories)
│   │   ├── base.repository.ts
│   │   ├── collection.repository.ts
│   │   ├── word.repository.ts
│   │   ├── user.repository.ts
│   │   ├── keyword.repository.ts
│   │   ├── paragraph.repository.ts
│   │   ├── feedback.repository.ts
│   │   ├── last-seen-word.repository.ts
│   │   └── collection-stats.repository.ts
│   ├── middleware/          # Server middleware
│   │   └── auth.middleware.ts
│   ├── schemas/             # MongoDB schemas & validation
│   ├── errors/              # Custom error classes
│   │   ├── application.errors.ts
│   │   └── index.ts
│   ├── utils/               # Backend utilities
│   │   └── token.util.ts
│   ├── config/              # Backend configuration
│   │   └── genkit.config.ts
│   ├── cache-init.server.ts # Cache initialization
│   └── wire.ts              # Dependency injection container
├── components/              # UI Components Library
│   ├── ui/                  # Base UI primitives (25+ components)
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   ├── form.tsx
│   │   ├── input.tsx
│   │   ├── loading-spinner.tsx
│   │   ├── navigation.tsx
│   │   ├── progress-bar.tsx
│   │   ├── select.tsx
│   │   ├── tabs.tsx
│   │   ├── textarea.tsx
│   │   ├── theme-provider.tsx
│   │   ├── translate.tsx
│   │   └── ...
│   ├── auth/                # Authentication components
│   │   ├── auth-guard.tsx
│   │   ├── google-login-button.tsx
│   │   └── login-form.tsx
│   ├── dashboard/           # Dashboard components
│   │   ├── token-monitoring-dashboard.tsx
│   │   ├── token-usage-chart.tsx
│   │   ├── cost-trend-chart.tsx
│   │   ├── cache-performance.tsx
│   │   └── ...
│   ├── floating-ui/         # Floating UI system
│   │   ├── floating-ui-manager.tsx
│   │   └── simple-enhanced-floating-buttons.tsx
│   ├── home/                # Home page components
│   │   ├── hero-section.tsx
│   │   ├── collections-section.tsx
│   │   ├── feature-card.tsx
│   │   └── ...
│   ├── layout/              # Layout components
│   │   └── header.tsx
│   ├── onboarding/          # User onboarding
│   │   ├── guidance-panel.tsx
│   │   ├── quick-start-guide.tsx
│   │   ├── welcome-banner.tsx
│   │   └── ...
│   ├── settings/            # Settings components
│   │   └── enhanced-settings-panel.tsx
│   ├── feedback/            # Feedback components
│   │   └── enhanced-feedback-form.tsx
│   ├── error/               # Error handling components
│   │   ├── error-boundary.tsx
│   │   └── error-page.tsx
│   ├── fallback/            # Fallback components
│   │   └── fallback-components.tsx
│   └── animations/          # Animation definitions
│       ├── feedback-animations.ts
│       └── settings-animations.ts
├── contexts/                # React Context Providers (11 contexts)
│   ├── auth-context.tsx
│   ├── collections-context.tsx
│   ├── translation-context.tsx
│   ├── toast-context.tsx
│   ├── loading-context.tsx
│   ├── error-context.tsx
│   ├── floating-ui-context.tsx
│   ├── simple-floating-context.tsx
│   ├── guidance-context.tsx
│   ├── keywords-context.tsx
│   ├── last-seen-word-context.tsx
│   ├── llm-context.tsx
│   ├── media-query-context.tsx
│   ├── translations/        # Translation files
│   │   ├── nav.ts
│   │   ├── home.ts
│   │   ├── collections.ts
│   │   ├── ui.ts
│   │   ├── errors.ts
│   │   └── translation-dict.ts
│   └── index.ts
├── hooks/                   # Custom React Hooks (9 hooks)
│   ├── use-collections.ts
│   ├── use-words.ts
│   ├── use-floating-ui.ts
│   ├── use-dom-floating.ts
│   ├── use-floating-position.ts
│   ├── use-simple-floating.ts
│   ├── use-media-query.ts
│   ├── use-page-guidance.ts
│   ├── use-graceful-degradation.ts
│   ├── use-undo-actions.ts
│   └── index.ts
├── lib/                     # Shared Utilities & Integrations
│   ├── auth.ts
│   ├── error-handling.ts
│   ├── error-management.ts
│   ├── error-reporting.ts
│   ├── error-integration.ts
│   ├── api-interceptor.ts
│   ├── api-error-middleware.ts
│   ├── form-error-handler.ts
│   ├── debounce.ts
│   ├── retry.ts
│   ├── text-diff.ts
│   ├── utils.ts
│   ├── indexed-db/
│   ├── api/
│   └── index.ts
├── models/                  # Domain Models (5 models)
│   ├── collection.ts
│   ├── word.ts
│   ├── user.ts
│   ├── keyword.ts
│   ├── paragraph.ts
│   └── index.ts
├── types/                   # TypeScript Type Definitions
│   ├── floating-ui.ts
│   └── index.ts
├── config/                  # Configuration Management
│   ├── config.ts
│   ├── client.config.ts
│   ├── server.config.ts
│   └── index.ts
├── constants/               # Application Constants
│   ├── loading-keys.ts
│   ├── loading-scopes.ts
│   └── index.ts
├── providers/               # React Providers
│   └── error-management-provider.tsx
├── styles/                  # Custom CSS Styles
│   ├── floating-ui.css
│   └── masonry.css
├── test/                    # Testing Infrastructure
│   ├── fixtures/
│   ├── helpers/
│   ├── mocks/
│   ├── setup.ts
│   └── vitest-setup.ts
└── middleware.ts            # Next.js middleware
```

---

**Tài liệu này được cập nhật thường xuyên để phản ánh kiến trúc hiện tại của dự án.**
