# Tính năng Đổi mật khẩu (Change Password Feature)

## Tổng quan

Tính năng đổi mật khẩu cho phép người dùng cập nhật mật khẩu tài khoản của họ một cách an toàn. Tính năng này chỉ khả dụng cho tài khoản sử dụng phương thức đăng nhập bằng username/password.

## Kiến trúc

### Backend Components

#### 1. AuthService Extension
- **File**: `src/backend/services/auth.service.ts`
- **Method**: `changePassword(userId, currentPassword, newPassword, ipAddress?, userAgent?)`
- **Chức năng**:
  - <PERSON><PERSON><PERSON> thực mật khẩu hiện tại
  - Validate mật khẩu mới
  - Hash mật khẩu mới với bcrypt
  - Cập nhật database
  - Ghi log audit

#### 2. UserService Extension
- **File**: `src/backend/services/user.service.ts`
- **Method**: `updatePassword(userId, passwordHash)`
- **Chức năng**: Cập nhật password_hash trong database

#### 3. API Endpoint
- **File**: `src/app/api/user/change-password/route.ts`
- **Method**: POST
- **Authentication**: Required (JWT token)
- **Request Body**:
  ```json
  {
    "currentPassword": "string",
    "newPassword": "string"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Password changed successfully"
  }
  ```

### Frontend Components

#### 1. ChangePasswordForm Component
- **File**: `src/components/auth/change-password-form.tsx`
- **Features**:
  - Form validation (client-side)
  - Password visibility toggle
  - Loading states
  - Error handling
  - Success notifications

#### 2. Settings Panel Integration
- **File**: `src/components/settings/enhanced-settings-panel.tsx`
- **Features**:
  - Security section trong User settings
  - Modal dialog cho change password form
  - Conditional rendering (chỉ hiện cho USERNAME_PASSWORD accounts)

## Security Features

### 1. Authentication & Authorization
- Yêu cầu JWT token hợp lệ
- Chỉ user có thể đổi mật khẩu của chính họ
- Chỉ hỗ trợ USERNAME_PASSWORD provider

### 2. Password Validation
- **Current Password**: Phải chính xác
- **New Password**: Tối thiểu 6 ký tự
- **Confirm Password**: Phải khớp với new password

### 3. Security Measures
- Hash password với bcrypt (salt rounds: 10)
- Rate limiting (inherited từ middleware)
- Audit logging cho tất cả attempts
- IP address và User Agent tracking

### 4. Audit Logging
- **Successful Change**: Action `CHANGE_PASSWORD`
- **Failed Attempts**: Action `CHANGE_PASSWORD_FAILED`
- **Logged Data**: User ID, IP address, User Agent, timestamp

## Internationalization

### Translation Keys
- `auth.change_password.title`
- `auth.change_password.description`
- `auth.change_password.current_password_label`
- `auth.change_password.new_password_label`
- `auth.change_password.confirm_password_label`
- `auth.change_password.submit`
- `auth.change_password.success`
- `auth.change_password.error`
- `auth.change_password.passwords_not_match`
- `settings.change_password`
- `settings.security`

### Supported Languages
- English (EN)
- Vietnamese (VI)

## User Experience

### 1. Access Points
- **Settings Panel**: Click vào Settings icon → Security section → "Change Password" button
- **Modal Dialog**: Form hiện trong modal overlay

### 2. User Flow
1. User mở Settings panel
2. Nếu account type là USERNAME_PASSWORD, hiện Security section
3. Click "Change Password" button
4. Modal dialog mở với ChangePasswordForm
5. User nhập current password, new password, confirm password
6. Submit form
7. Success notification và form reset
8. Modal đóng

### 3. Error Handling
- **Client-side validation**: Real-time validation
- **Server-side errors**: Hiển thị error messages
- **Network errors**: Graceful error handling
- **Toast notifications**: Success/error feedback

## API Documentation

### POST /api/user/change-password

#### Request
```http
POST /api/user/change-password
Content-Type: application/json
Cookie: auth_token=<jwt_token>

{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword123"
}
```

#### Responses

**Success (200)**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

**Validation Error (400)**
```json
{
  "success": false,
  "error": "Validation error",
  "details": [
    {
      "code": "too_small",
      "minimum": 6,
      "type": "string",
      "inclusive": true,
      "exact": false,
      "message": "New password must be at least 6 characters long",
      "path": ["newPassword"]
    }
  ]
}
```

**Authentication Error (401)**
```json
{
  "success": false,
  "error": "Unauthorized"
}
```

**Business Logic Error (400)**
```json
{
  "success": false,
  "error": "Current password is incorrect"
}
```

**Server Error (500)**
```json
{
  "success": false,
  "error": "Internal server error"
}
```

## Testing

### Manual Testing Steps

1. **Setup**:
   - Tạo tài khoản với username/password
   - Đăng nhập vào ứng dụng

2. **Access Feature**:
   - Click Settings icon
   - Verify Security section hiện ra
   - Click "Change Password" button

3. **Form Validation**:
   - Test empty fields
   - Test short password
   - Test mismatched passwords

4. **Password Change**:
   - Nhập correct current password
   - Nhập valid new password
   - Confirm new password
   - Submit form

5. **Verification**:
   - Check success notification
   - Logout và login lại với new password
   - Verify old password không work

### Edge Cases

1. **Non-USERNAME_PASSWORD accounts**: Security section không hiện
2. **Invalid current password**: Error message hiển thị
3. **Network errors**: Graceful error handling
4. **Concurrent requests**: Proper handling

## Deployment Notes

### Database Requirements
- Prisma schema đã có `password_hash` field
- Migrations đã được apply

### Environment Variables
- Không cần thêm environment variables mới
- Sử dụng existing JWT và bcrypt configuration

### Security Considerations
- Rate limiting được handle bởi middleware
- HTTPS required trong production
- Secure cookie settings

## Future Enhancements

### Potential Improvements
1. **Password Strength Meter**: Visual indicator cho password strength
2. **Password History**: Prevent reusing recent passwords
3. **Email Notifications**: Notify user về password changes
4. **Two-Factor Authentication**: Additional security layer
5. **Password Expiry**: Force periodic password changes
6. **Breach Detection**: Check against known breached passwords

### Technical Debt
1. **Rate Limiting**: Implement specific rate limiting cho change password
2. **Comprehensive Testing**: Unit tests, integration tests, E2E tests
3. **Error Monitoring**: Enhanced error tracking và alerting
4. **Performance Optimization**: Optimize bcrypt operations

## Troubleshooting

### Common Issues

1. **"Password change not supported for this account type"**
   - User đang sử dụng Telegram hoặc Google login
   - Solution: Chỉ USERNAME_PASSWORD accounts có thể đổi password

2. **"Current password is incorrect"**
   - User nhập sai current password
   - Solution: Verify current password

3. **Database errors**
   - Check database connection
   - Verify Prisma schema và migrations

4. **JWT errors**
   - Check authentication middleware
   - Verify JWT token validity

### Debug Steps
1. Check browser console cho client-side errors
2. Check server logs cho API errors
3. Verify database state
4. Test API endpoints directly với tools như Postman
