# Idiomatic Expressions Development Plan

## Overview

Implement a comprehensive system for learning and practicing idiomatic expressions, helping users understand and use phrases that don't translate literally but are essential for natural language communication.

## Technical Architecture

### Database Schema Extensions

```prisma
model IdiomaticExpression {
  id              String   @id @default(uuid())
  expression      String
  language        Language
  literal_meaning String?
  actual_meaning  String
  usage_context   String[]
  formality_level FormalityLevel
  frequency_score Float    @default(1.0)
  difficulty      Difficulty
  origin_story    String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  examples        IdiomExample[]
  translations    IdiomTranslation[]
  categories      IdiomCategory[]
  userProgress    UserIdiomProgress[]

  @@unique([expression, language])
  @@index([language])
  @@index([difficulty])
  @@index([frequency_score])
}

model IdiomExample {
  id          String   @id @default(uuid())
  idiom_id    String
  example     String
  context     String?
  translation String?
  audio_url   String?
  created_at  DateTime @default(now())

  idiom       IdiomaticExpression @relation(fields: [idiom_id], references: [id], onDelete: Cascade)

  @@index([idiom_id])
}

model IdiomTranslation {
  id               String   @id @default(uuid())
  idiom_id         String
  target_language  Language
  translation      String
  equivalent_idiom String?
  cultural_notes   String?
  created_at       DateTime @default(now())

  idiom            IdiomaticExpression @relation(fields: [idiom_id], references: [id], onDelete: Cascade)

  @@unique([idiom_id, target_language])
  @@index([idiom_id])
}

model IdiomCategory {
  id          String   @id @default(uuid())
  idiom_id    String
  category    String
  subcategory String?
  created_at  DateTime @default(now())

  idiom       IdiomaticExpression @relation(fields: [idiom_id], references: [id], onDelete: Cascade)

  @@unique([idiom_id, category])
  @@index([category])
}

model UserIdiomProgress {
  id              String   @id @default(uuid())
  user_id         String
  idiom_id        String
  mastery_level   MasteryLevel @default(LEARNING)
  last_practiced  DateTime?
  practice_count  Int      @default(0)
  correct_uses    Int      @default(0)
  incorrect_uses  Int      @default(0)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  user            User     @relation(fields: [user_id], references: [id])
  idiom           IdiomaticExpression @relation(fields: [idiom_id], references: [id])

  @@unique([user_id, idiom_id])
  @@index([user_id])
  @@index([mastery_level])
}

enum FormalityLevel {
  VERY_FORMAL
  FORMAL
  NEUTRAL
  INFORMAL
  VERY_INFORMAL
  SLANG
}

enum MasteryLevel {
  LEARNING
  PRACTICING
  FAMILIAR
  MASTERED
}
```

### Service Layer Implementation

#### Idiomatic Expression Service

```typescript
interface IdiomaticExpressionService {
	getIdiomById(idiomId: string): Promise<IdiomaticExpressionWithDetails>;
	searchIdioms(query: string, filters: IdiomSearchFilters): Promise<IdiomaticExpression[]>;
	getIdiomsByCategory(category: string, language: Language): Promise<IdiomaticExpression[]>;
	getRandomIdioms(count: number, difficulty?: Difficulty): Promise<IdiomaticExpression[]>;
	addIdiom(idiom: CreateIdiomRequest): Promise<IdiomaticExpression>;
	updateIdiom(idiomId: string, updates: UpdateIdiomRequest): Promise<IdiomaticExpression>;
	deleteIdiom(idiomId: string): Promise<void>;

	// User progress methods
	getUserIdiomProgress(userId: string, idiomId: string): Promise<UserIdiomProgress>;
	updateUserProgress(
		userId: string,
		idiomId: string,
		result: PracticeResult
	): Promise<UserIdiomProgress>;
	getUserMasteredIdioms(userId: string): Promise<IdiomaticExpression[]>;
	getRecommendedIdioms(userId: string, count: number): Promise<IdiomaticExpression[]>;
}

interface IdiomaticExpressionWithDetails extends IdiomaticExpression {
	examples: IdiomExample[];
	translations: IdiomTranslation[];
	categories: IdiomCategory[];
	userProgress?: UserIdiomProgress;
}

interface IdiomSearchFilters {
	language?: Language;
	difficulty?: Difficulty;
	formalityLevel?: FormalityLevel;
	category?: string;
	minFrequency?: number;
	maxFrequency?: number;
}

interface PracticeResult {
	correct: boolean;
	timeSpent: number;
	exerciseType: string;
	confidence: number;
}
```

#### Idiom Practice Service

```typescript
interface IdiomPracticeService {
	generateMatchingExercise(idiomIds: string[]): Promise<MatchingExercise>;
	generateContextExercise(idiomId: string): Promise<ContextExercise>;
	generateTranslationExercise(idiomId: string): Promise<TranslationExercise>;
	generateUsageExercise(idiomId: string): Promise<UsageExercise>;
	evaluateAnswer(exerciseId: string, answer: string): Promise<ExerciseResult>;
	getPersonalizedPracticeSet(userId: string, count: number): Promise<IdiomaticExpression[]>;
}

interface MatchingExercise {
	id: string;
	type: 'matching';
	idioms: string[];
	meanings: string[];
	timeLimit: number;
}

interface ContextExercise {
	id: string;
	type: 'context';
	sentence: string;
	missingIdiom: string;
	options: string[];
	hint?: string;
}

interface TranslationExercise {
	id: string;
	type: 'translation';
	idiom: string;
	sourceLanguage: Language;
	targetLanguage: Language;
	hint?: string;
}

interface UsageExercise {
	id: string;
	type: 'usage';
	scenario: string;
	idiom: string;
	correctUsage: boolean;
	explanation: string;
}
```

### Frontend Components

#### Idiom Card Component

```typescript
interface IdiomCardProps {
	idiom: IdiomaticExpressionWithDetails;
	showTranslation?: boolean;
	showExamples?: boolean;
	onPractice?: (idiomId: string) => void;
	onBookmark?: (idiomId: string) => void;
}

export function IdiomCard({
	idiom,
	showTranslation = true,
	showExamples = true,
	onPractice,
	onBookmark,
}: IdiomCardProps) {
	// Component implementation with:
	// - Expression display with pronunciation
	// - Literal vs actual meaning comparison
	// - Usage examples with audio
	// - Formality level indicator
	// - Practice and bookmark buttons
}
```

#### Idiom Practice Interface

```typescript
interface IdiomPracticeInterfaceProps {
	userId: string;
	practiceType: 'daily' | 'review' | 'challenge';
	onComplete: (results: PracticeSession) => void;
}

export function IdiomPracticeInterface({
	userId,
	practiceType,
	onComplete,
}: IdiomPracticeInterfaceProps) {
	// Component implementation with:
	// - Exercise type rotation
	// - Progress tracking
	// - Immediate feedback
	// - Streak tracking
}
```

#### Idiom Explorer

```typescript
interface IdiomExplorerProps {
	language: Language;
	initialCategory?: string;
	onIdiomSelect: (idiom: IdiomaticExpression) => void;
}

export function IdiomExplorer({ language, initialCategory, onIdiomSelect }: IdiomExplorerProps) {
	// Component implementation with:
	// - Category browsing
	// - Search and filtering
	// - Difficulty progression
	// - Bookmarking system
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (3 weeks)

1. **Database Schema Setup**
    - Create idiom-related models
    - Set up relationships and indexes
    - Create migration scripts

2. **Repository Layer**
    - Implement IdiomaticExpressionRepository
    - Create UserIdiomProgressRepository
    - Add complex query methods

3. **Service Layer Foundation**
    - Create IdiomaticExpressionService
    - Implement IdiomPracticeService
    - Add basic CRUD operations

### Phase 2: Content Management (2 weeks)

1. **Admin Interface**
    - Idiom creation and editing tools
    - Bulk import functionality
    - Content validation system

2. **Data Population**
    - Research common idioms
    - Create example sentences
    - Add translations and cultural notes

### Phase 3: Practice System (3 weeks)

1. **Exercise Generation**
    - Multiple exercise types
    - Adaptive difficulty
    - Personalized recommendations

2. **Progress Tracking**
    - Mastery level calculation
    - Spaced repetition integration
    - Performance analytics

### Phase 4: User Interface (3 weeks)

1. **Core Components**
    - IdiomCard component
    - Practice interface
    - Explorer and browser

2. **User Experience**
    - Smooth animations
    - Audio integration
    - Mobile optimization

## Content Strategy

### Idiom Categories

- **Emotions**: Happy as a clam, feeling blue
- **Time**: Better late than never, time flies
- **Money**: Break the bank, penny for your thoughts
- **Weather**: Under the weather, storm in a teacup
- **Animals**: Let the cat out of the bag, hold your horses
- **Food**: Piece of cake, spill the beans
- **Body Parts**: Break a leg, keep your eyes peeled

### Cultural Considerations

- Regional variations of idioms
- Cultural context explanations
- Appropriate usage situations
- Formality level guidance

### Quality Assurance

- Native speaker validation
- Cultural accuracy review
- Usage frequency verification
- Regular content updates

## Learning Methodology

### Spaced Repetition Integration

- Idioms scheduled based on mastery level
- Increased intervals for mastered idioms
- Review reminders for forgotten idioms

### Contextual Learning

- Real-world usage examples
- Situational practice scenarios
- Cultural background stories

### Progressive Difficulty

- Start with common, simple idioms
- Gradually introduce complex expressions
- Advanced cultural and historical idioms

## Analytics & Metrics

### Learning Effectiveness

- Mastery progression rates
- Retention after practice sessions
- Usage accuracy in exercises

### Content Quality

- User ratings for idiom explanations
- Difficulty level accuracy
- Translation quality feedback

### Engagement Metrics

- Daily practice completion rates
- Favorite idiom categories
- Time spent on different exercise types

## Success Criteria

### User Engagement

- 60% of users complete daily idiom practice
- 80% retention rate for practiced idioms
- 4.5+ star rating for idiom content

### Learning Outcomes

- 70% improvement in idiom recognition tests
- 85% correct usage in context exercises
- 90% user satisfaction with explanations

### Technical Performance

- Sub-300ms response times for idiom queries
- 99.9% uptime for practice features
- Smooth performance on mobile devices
