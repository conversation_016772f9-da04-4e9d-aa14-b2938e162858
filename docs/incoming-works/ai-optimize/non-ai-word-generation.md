# Non-AI Word Generation Strategy

## 🎯 Mục Tiêu
<PERSON> 60-80% chi phí AI bằng cách chuyển đổi các tác vụ đơn giản sang non-AI solutions.

## 📊 Phân Tích Tiềm Năng Tiết Kiệm

### Tác Vụ Có Thể Chuyển Đổi Hoàn Toàn:

#### 1. **Basic Word Generation** (Tiết kiệm: 90%)
```typescript
// Hiện tại: AI call cho generateRandomTerms
// Chi phí: ~50-100 tokens/request

// Thay thế: Dictionary-based generation
interface WordDatabase {
  [category: string]: {
    [difficulty: string]: {
      [language: string]: string[];
    }
  }
}

class NonAIWordGenerator {
  private wordDB: WordDatabase;
  
  generateRandomTerms(params: {
    keywords: string[];
    category: string;
    difficulty: Difficulty;
    count: number;
    excludes: string[];
  }): string[] {
    // Lọc từ database dựa trên keywords + category
    // Random selection với weighted algorithm
    // Cost: 0 tokens, chỉ có computation cost
  }
}
```

#### 2. **Basic Translation Pairs** (Tiết kiệm: 80%)
```typescript
// Tạo translation database từ các nguồn mở
interface TranslationDatabase {
  [term: string]: {
    [language: string]: {
      definition: string;
      examples: string[];
      partOfSpeech: string;
      difficulty: number;
    }
  }
}
```

#### 3. **Template-based Content** (Tiết kiệm: 70%)
```typescript
// Paragraph generation với templates
class TemplateBasedGenerator {
  generateParagraph(keywords: string[], difficulty: Difficulty): string {
    // Sử dụng predefined templates + keyword substitution
    // Madlibs-style generation
  }
}
```

### Tác Vụ Hybrid (AI + Non-AI):

#### 1. **Smart Word Details** (Tiết kiệm: 50%)
```typescript
class HybridWordGenerator {
  async generateWordDetails(terms: string[]): Promise<WordDetail[]> {
    // Step 1: Check local database first
    const fromDB = await this.getFromDatabase(terms);
    
    // Step 2: Only call AI for missing terms
    const missing = terms.filter(t => !fromDB.includes(t));
    const fromAI = missing.length > 0 ? await this.callAI(missing) : [];
    
    // Step 3: Cache AI results to database
    await this.cacheToDatabase(fromAI);
    
    return [...fromDB, ...fromAI];
  }
}
```

## 🛠️ Implementation Plan

### Phase 1: Database Creation (Week 1-2)
1. **Scrape Open Dictionaries**
   - Wiktionary API
   - OpenTapioca
   - ConceptNet
   - FreeDictionary API

2. **Build Word Classification System**
   - Category mapping (technology, food, travel, etc.)
   - Difficulty scoring algorithm
   - Frequency analysis

### Phase 2: Non-AI Generators (Week 3-4)
1. **Word Generator Service**
2. **Template Engine**
3. **Translation Cache System**

### Phase 3: Hybrid Integration (Week 5-6)
1. **Fallback Mechanisms**
2. **Quality Scoring**
3. **User Feedback Integration**

## 📈 Expected Results
- **Cost Reduction**: 60-80% overall
- **Response Time**: 5-10x faster
- **Scalability**: No token limits
- **Quality**: 80-90% of AI quality for basic tasks

## 🔄 Fallback Strategy
- Non-AI first → AI if quality < threshold
- User feedback loop to improve non-AI quality
- Gradual expansion of non-AI capabilities
