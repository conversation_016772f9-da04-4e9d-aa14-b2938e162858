# Assignment System Development Plan

## Overview

Implement a comprehensive assignment system that allows educators to create, distribute, and track language learning assignments for students, with automated grading and detailed progress analytics.

## Technical Architecture

### Database Schema Extensions

```prisma
model Assignment {
  id              String   @id @default(uuid())
  title           String
  description     String?
  creator_id      String
  assignment_type AssignmentType
  difficulty      Difficulty
  estimated_time  Int      // minutes
  due_date        DateTime?
  is_published    Boolean  @default(false)
  auto_grade      Boolean  @default(true)
  max_attempts    Int      @default(1)
  passing_score   Float    @default(0.7)
  instructions    String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  creator         User     @relation("CreatedAssignments", fields: [creator_id], references: [id])
  content         AssignmentContent[]
  submissions     AssignmentSubmission[]
  distributions   AssignmentDistribution[]

  @@index([creator_id])
  @@index([assignment_type])
  @@index([due_date])
}

model AssignmentContent {
  id            String   @id @default(uuid())
  assignment_id String
  content_type  ContentType
  content_id    String   // Reference to Word, Paragraph, etc.
  order_index   Int
  points        Float    @default(1.0)
  instructions  String?
  created_at    DateTime @default(now())

  assignment    Assignment @relation(fields: [assignment_id], references: [id], onDelete: Cascade)

  @@unique([assignment_id, order_index])
  @@index([assignment_id])
}

model AssignmentDistribution {
  id            String   @id @default(uuid())
  assignment_id String
  target_type   TargetType
  target_id     String   // User ID, Group ID, or Class ID
  assigned_by   String
  assigned_at   DateTime @default(now())
  due_date      DateTime?

  assignment    Assignment @relation(fields: [assignment_id], references: [id], onDelete: Cascade)
  assigner      User       @relation("AssignedAssignments", fields: [assigned_by], references: [id])

  @@unique([assignment_id, target_type, target_id])
  @@index([target_type, target_id])
}

model AssignmentSubmission {
  id            String   @id @default(uuid())
  assignment_id String
  student_id    String
  attempt_number Int     @default(1)
  status        SubmissionStatus @default(IN_PROGRESS)
  score         Float?
  max_score     Float
  time_spent    Int      @default(0) // seconds
  started_at    DateTime @default(now())
  submitted_at  DateTime?
  graded_at     DateTime?
  feedback      String?

  assignment    Assignment @relation(fields: [assignment_id], references: [id])
  student       User       @relation("StudentSubmissions", fields: [student_id], references: [id])
  answers       SubmissionAnswer[]

  @@unique([assignment_id, student_id, attempt_number])
  @@index([student_id])
  @@index([status])
}

model SubmissionAnswer {
  id            String   @id @default(uuid())
  submission_id String
  content_id    String   // Reference to AssignmentContent
  answer        Json     // Flexible answer format
  is_correct    Boolean?
  points_earned Float    @default(0)
  time_spent    Int      @default(0) // seconds
  created_at    DateTime @default(now())

  submission    AssignmentSubmission @relation(fields: [submission_id], references: [id], onDelete: Cascade)

  @@unique([submission_id, content_id])
  @@index([submission_id])
}

model StudentGroup {
  id          String   @id @default(uuid())
  name        String
  description String?
  teacher_id  String
  code        String   @unique
  is_active   Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  teacher     User     @relation("TeacherGroups", fields: [teacher_id], references: [id])
  members     GroupMembership[]

  @@index([teacher_id])
  @@index([code])
}

model GroupMembership {
  id         String   @id @default(uuid())
  group_id   String
  student_id String
  joined_at  DateTime @default(now())
  is_active  Boolean  @default(true)

  group      StudentGroup @relation(fields: [group_id], references: [id], onDelete: Cascade)
  student    User         @relation("StudentGroups", fields: [student_id], references: [id])

  @@unique([group_id, student_id])
  @@index([student_id])
}

enum AssignmentType {
  VOCABULARY_PRACTICE
  READING_COMPREHENSION
  LISTENING_EXERCISE
  WRITING_TASK
  SPEAKING_PRACTICE
  MIXED_SKILLS
  QUIZ
  EXAM
}

enum ContentType {
  WORD
  PARAGRAPH
  AUDIO
  IMAGE
  CUSTOM_QUESTION
}

enum TargetType {
  INDIVIDUAL
  GROUP
  CLASS
}

enum SubmissionStatus {
  NOT_STARTED
  IN_PROGRESS
  SUBMITTED
  GRADED
  LATE_SUBMISSION
}
```

### Service Layer Implementation

#### Assignment Service

```typescript
interface AssignmentService {
	createAssignment(creatorId: string, assignment: CreateAssignmentRequest): Promise<Assignment>;
	updateAssignment(assignmentId: string, updates: UpdateAssignmentRequest): Promise<Assignment>;
	deleteAssignment(assignmentId: string): Promise<void>;
	getAssignmentById(assignmentId: string): Promise<AssignmentWithDetails>;
	getAssignmentsByCreator(creatorId: string, filters?: AssignmentFilters): Promise<Assignment[]>;
	publishAssignment(assignmentId: string): Promise<Assignment>;
	duplicateAssignment(assignmentId: string, creatorId: string): Promise<Assignment>;

	// Content management
	addContentToAssignment(
		assignmentId: string,
		content: AssignmentContentRequest
	): Promise<AssignmentContent>;
	updateAssignmentContent(
		contentId: string,
		updates: Partial<AssignmentContent>
	): Promise<AssignmentContent>;
	removeContentFromAssignment(contentId: string): Promise<void>;
	reorderAssignmentContent(assignmentId: string, contentOrder: string[]): Promise<void>;
}

interface AssignmentDistributionService {
	distributeAssignment(
		assignmentId: string,
		distribution: DistributionRequest
	): Promise<AssignmentDistribution>;
	getStudentAssignments(
		studentId: string,
		status?: SubmissionStatus
	): Promise<StudentAssignmentView[]>;
	getTeacherAssignments(
		teacherId: string,
		filters?: TeacherAssignmentFilters
	): Promise<TeacherAssignmentView[]>;
	removeAssignmentDistribution(distributionId: string): Promise<void>;
	updateDueDate(distributionId: string, newDueDate: DateTime): Promise<AssignmentDistribution>;
}

interface AssignmentSubmissionService {
	startAssignment(assignmentId: string, studentId: string): Promise<AssignmentSubmission>;
	saveAnswer(submissionId: string, contentId: string, answer: any): Promise<SubmissionAnswer>;
	submitAssignment(submissionId: string): Promise<AssignmentSubmission>;
	gradeSubmission(submissionId: string): Promise<AssignmentSubmission>;
	getSubmissionDetails(submissionId: string): Promise<SubmissionWithDetails>;
	getStudentSubmissions(
		studentId: string,
		assignmentId?: string
	): Promise<AssignmentSubmission[]>;
	getAssignmentSubmissions(assignmentId: string): Promise<SubmissionSummary[]>;
	provideFeedback(submissionId: string, feedback: string): Promise<AssignmentSubmission>;
}

interface StudentGroupService {
	createGroup(teacherId: string, group: CreateGroupRequest): Promise<StudentGroup>;
	updateGroup(groupId: string, updates: UpdateGroupRequest): Promise<StudentGroup>;
	deleteGroup(groupId: string): Promise<void>;
	addStudentToGroup(groupId: string, studentId: string): Promise<GroupMembership>;
	removeStudentFromGroup(groupId: string, studentId: string): Promise<void>;
	getGroupsByTeacher(teacherId: string): Promise<StudentGroup[]>;
	getGroupsByStudent(studentId: string): Promise<StudentGroup[]>;
	joinGroupByCode(studentId: string, groupCode: string): Promise<GroupMembership>;
}
```

### Frontend Components

#### Assignment Creator

```typescript
interface AssignmentCreatorProps {
	onAssignmentCreated: (assignment: Assignment) => void;
	initialData?: Partial<Assignment>;
}

export function AssignmentCreator({ onAssignmentCreated, initialData }: AssignmentCreatorProps) {
	// Component implementation with:
	// - Assignment metadata form
	// - Content selection and ordering
	// - Preview functionality
	// - Publishing controls
}
```

#### Assignment Dashboard

```typescript
interface AssignmentDashboardProps {
	userRole: 'teacher' | 'student';
	userId: string;
}

export function AssignmentDashboard({ userRole, userId }: AssignmentDashboardProps) {
	// Component implementation with:
	// - Role-specific views
	// - Assignment lists and filters
	// - Quick actions
	// - Progress overview
}
```

#### Assignment Player

```typescript
interface AssignmentPlayerProps {
	assignmentId: string;
	studentId: string;
	onComplete: (submission: AssignmentSubmission) => void;
}

export function AssignmentPlayer({ assignmentId, studentId, onComplete }: AssignmentPlayerProps) {
	// Component implementation with:
	// - Question navigation
	// - Auto-save functionality
	// - Timer display
	// - Progress tracking
}
```

#### Grading Interface

```typescript
interface GradingInterfaceProps {
	submissionId: string;
	onGradingComplete: (submission: AssignmentSubmission) => void;
}

export function GradingInterface({ submissionId, onGradingComplete }: GradingInterfaceProps) {
	// Component implementation with:
	// - Answer review
	// - Manual grading tools
	// - Feedback provision
	// - Grade calculation
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (4 weeks)

1. **Database Schema Setup**
    - Create all assignment-related models
    - Set up relationships and indexes
    - Create migration scripts

2. **Repository Layer**
    - Implement all repository classes
    - Add complex query methods
    - Set up data access patterns

3. **Service Layer Foundation**
    - Create core services
    - Implement basic CRUD operations
    - Add business logic

### Phase 2: Assignment Creation & Management (3 weeks)

1. **Assignment Creator**
    - Metadata management
    - Content selection interface
    - Preview and validation

2. **Content Management**
    - Drag-and-drop ordering
    - Content type handlers
    - Bulk operations

### Phase 3: Distribution & Student Experience (3 weeks)

1. **Distribution System**
    - Group management
    - Assignment distribution
    - Due date management

2. **Student Interface**
    - Assignment dashboard
    - Assignment player
    - Progress tracking

### Phase 4: Grading & Analytics (3 weeks)

1. **Auto-Grading System**
    - Answer evaluation algorithms
    - Score calculation
    - Feedback generation

2. **Manual Grading Tools**
    - Grading interface
    - Feedback provision
    - Grade management

3. **Analytics Dashboard**
    - Performance metrics
    - Progress tracking
    - Reporting tools

## Auto-Grading Algorithms

### Vocabulary Questions

```typescript
interface VocabularyGrader {
	gradeDefinitionMatch(answer: string, correct: string): GradeResult;
	gradeSynonymSelection(selected: string[], correct: string[]): GradeResult;
	gradeUsageInContext(answer: string, context: string, word: string): GradeResult;
}
```

### Reading Comprehension

```typescript
interface ReadingGrader {
	gradeMultipleChoice(selected: string, correct: string): GradeResult;
	gradeShortAnswer(answer: string, expectedKeywords: string[]): GradeResult;
	gradeSummary(summary: string, originalText: string): GradeResult;
}
```

### Writing Assessment

```typescript
interface WritingGrader {
	gradeGrammar(text: string): GrammarGradeResult;
	gradeVocabularyUsage(text: string, targetWords: string[]): VocabularyGradeResult;
	gradeCoherence(text: string): CoherenceGradeResult;
}
```

## Analytics & Reporting

### Student Analytics

- Individual progress tracking
- Skill area performance
- Time management analysis
- Improvement recommendations

### Teacher Analytics

- Class performance overview
- Assignment effectiveness metrics
- Student engagement analysis
- Curriculum gap identification

### Administrative Analytics

- System usage statistics
- Performance benchmarks
- Resource utilization
- Success rate analysis

## Integration Points

### LMS Integration

- Canvas, Moodle, Google Classroom
- Grade passback functionality
- Single sign-on support
- Content sharing protocols

### Assessment Tools

- Plagiarism detection
- AI-powered essay scoring
- Speech recognition for pronunciation
- Image recognition for visual tasks

## Success Criteria

### Teacher Adoption

- 80% of teachers create at least one assignment monthly
- 90% satisfaction with assignment creation tools
- 70% reduction in grading time

### Student Engagement

- 85% assignment completion rate
- 75% improvement in learning outcomes
- 90% positive feedback on user experience

### Technical Performance

- Sub-500ms response times for assignment loading
- 99.9% uptime during peak usage
- Zero data loss in submissions
