# Community Leaderboards Development Plan

## Overview

Implement a comprehensive leaderboard system that fosters healthy competition and community engagement through various ranking categories, time periods, and social features in the vocabulary learning application.

## Technical Architecture

### Database Schema Extensions

#### Leaderboard Models

```prisma
model Leaderboard {
  id          String           @id @default(uuid())
  name        String
  description String?
  category    LeaderboardCategory
  period      LeaderboardPeriod
  isActive    Boolean          @default(true)
  startDate   DateTime?
  endDate     DateTime?
  settings    Json?            // Custom settings for leaderboard
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  entries     LeaderboardEntry[]

  @@index([category, period, isActive])
  @@map("leaderboards")
}

model LeaderboardEntry {
  id            String      @id @default(uuid())
  leaderboardId String
  userId        String
  rank          Int
  score         Int
  previousRank  Int?
  metadata      Json?       // Additional data like streak, accuracy, etc.
  calculatedAt  DateTime    @default(now())

  leaderboard   Leaderboard @relation(fields: [leaderboardId], references: [id], onDelete: Cascade)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([leaderboardId, userId])
  @@index([leaderboardId, rank])
  @@index([userId])
  @@map("leaderboard_entries")
}

model UserLeaderboardStats {
  id                    String   @id @default(uuid())
  userId                String   @unique
  totalPoints           Int      @default(0)
  weeklyPoints          Int      @default(0)
  monthlyPoints         Int      @default(0)
  currentStreak         Int      @default(0)
  longestStreak         Int      @default(0)
  wordsLearned          Int      @default(0)
  paragraphsCompleted   Int      @default(0)
  averageAccuracy       Float    @default(0.0)
  totalTimeSpent        Int      @default(0) // in minutes
  lastUpdated           DateTime @default(now())

  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([totalPoints])
  @@index([weeklyPoints])
  @@index([monthlyPoints])
  @@index([currentStreak])
  @@map("user_leaderboard_stats")
}

enum LeaderboardCategory {
  TOTAL_POINTS
  WEEKLY_POINTS
  MONTHLY_POINTS
  CURRENT_STREAK
  LONGEST_STREAK
  WORDS_LEARNED
  PARAGRAPHS_COMPLETED
  ACCURACY
  TIME_SPENT
  LEVEL
}

enum LeaderboardPeriod {
  ALL_TIME
  YEARLY
  MONTHLY
  WEEKLY
  DAILY
}
```

#### User Model Extensions

```prisma
model User {
  // ... existing fields
  leaderboardEntries    LeaderboardEntry[]
  leaderboardStats      UserLeaderboardStats?
  isPublicProfile       Boolean              @default(true)
  displayName           String?
  avatar                String?
}
```

### Backend Implementation

#### Services

**Leaderboard Service** (`src/backend/services/leaderboard.service.ts`)

```typescript
export interface LeaderboardService {
	getLeaderboard(
		category: LeaderboardCategory,
		period: LeaderboardPeriod,
		limit?: number
	): Promise<LeaderboardEntry[]>;
	getUserRank(
		userId: string,
		category: LeaderboardCategory,
		period: LeaderboardPeriod
	): Promise<UserRankInfo>;
	updateUserStats(userId: string, statsUpdate: StatsUpdate): Promise<void>;
	calculateAllLeaderboards(): Promise<void>;
	getLeaderboardHistory(userId: string, category: LeaderboardCategory): Promise<RankHistory[]>;
	createCustomLeaderboard(config: CustomLeaderboardConfig): Promise<Leaderboard>;
}

export class LeaderboardServiceImpl implements LeaderboardService {
	constructor(
		private getLeaderboardRepository: () => LeaderboardRepository,
		private getUserLeaderboardStatsRepository: () => UserLeaderboardStatsRepository,
		private getCacheService: () => CacheService
	) {}

	async getLeaderboard(
		category: LeaderboardCategory,
		period: LeaderboardPeriod,
		limit: number = 100
	): Promise<LeaderboardEntry[]> {
		const cacheKey = `leaderboard:${category}:${period}:${limit}`;

		// Try to get from cache first
		const cached = await this.getCacheService().get(cacheKey);
		if (cached) return cached;

		const leaderboard = await this.getLeaderboardRepository().findByCategoryAndPeriod(
			category,
			period
		);

		if (!leaderboard) {
			// Create leaderboard if it doesn't exist
			await this.createLeaderboard(category, period);
			return this.getLeaderboard(category, period, limit);
		}

		const entries = await this.getLeaderboardRepository().getEntries(leaderboard.id, limit);

		// Cache for 5 minutes
		await this.getCacheService().set(cacheKey, entries, 300);

		return entries;
	}

	async getUserRank(
		userId: string,
		category: LeaderboardCategory,
		period: LeaderboardPeriod
	): Promise<UserRankInfo> {
		const leaderboard = await this.getLeaderboardRepository().findByCategoryAndPeriod(
			category,
			period
		);

		if (!leaderboard) {
			return { rank: null, score: 0, totalParticipants: 0 };
		}

		const entry = await this.getLeaderboardRepository().getUserEntry(leaderboard.id, userId);

		const totalParticipants = await this.getLeaderboardRepository().getParticipantCount(
			leaderboard.id
		);

		return {
			rank: entry?.rank || null,
			score: entry?.score || 0,
			previousRank: entry?.previousRank || null,
			totalParticipants,
			percentile: entry?.rank
				? Math.round((1 - (entry.rank - 1) / totalParticipants) * 100)
				: null,
		};
	}

	async updateUserStats(userId: string, statsUpdate: StatsUpdate): Promise<void> {
		const currentStats = await this.getUserLeaderboardStatsRepository().findByUserId(userId);

		const updatedStats = {
			totalPoints: currentStats.totalPoints + (statsUpdate.points || 0),
			weeklyPoints: this.calculateWeeklyPoints(currentStats, statsUpdate),
			monthlyPoints: this.calculateMonthlyPoints(currentStats, statsUpdate),
			currentStreak: statsUpdate.currentStreak ?? currentStats.currentStreak,
			longestStreak: Math.max(currentStats.longestStreak, statsUpdate.currentStreak || 0),
			wordsLearned: currentStats.wordsLearned + (statsUpdate.wordsLearned || 0),
			paragraphsCompleted:
				currentStats.paragraphsCompleted + (statsUpdate.paragraphsCompleted || 0),
			averageAccuracy: this.calculateAverageAccuracy(currentStats, statsUpdate),
			totalTimeSpent: currentStats.totalTimeSpent + (statsUpdate.timeSpent || 0),
			lastUpdated: new Date(),
		};

		await this.getUserLeaderboardStatsRepository().update(userId, updatedStats);

		// Trigger leaderboard recalculation for affected categories
		await this.scheduleLeaderboardUpdate([
			LeaderboardCategory.TOTAL_POINTS,
			LeaderboardCategory.WEEKLY_POINTS,
			LeaderboardCategory.MONTHLY_POINTS,
			LeaderboardCategory.CURRENT_STREAK,
			LeaderboardCategory.WORDS_LEARNED,
		]);
	}

	async calculateAllLeaderboards(): Promise<void> {
		const categories = Object.values(LeaderboardCategory);
		const periods = Object.values(LeaderboardPeriod);

		for (const category of categories) {
			for (const period of periods) {
				if (this.isValidCategoryPeriodCombination(category, period)) {
					await this.calculateLeaderboard(category, period);
				}
			}
		}
	}

	private async calculateLeaderboard(
		category: LeaderboardCategory,
		period: LeaderboardPeriod
	): Promise<void> {
		const leaderboard = await this.getLeaderboardRepository().findByCategoryAndPeriod(
			category,
			period
		);

		if (!leaderboard) return;

		const userStats = await this.getUserLeaderboardStatsRepository().getAllForPeriod(period);

		const sortedUsers = userStats
			.map((stats) => ({
				userId: stats.userId,
				score: this.getScoreForCategory(stats, category),
				metadata: this.getMetadataForCategory(stats, category),
			}))
			.filter((user) => user.score > 0)
			.sort((a, b) => b.score - a.score);

		// Update ranks
		const entries = sortedUsers.map((user, index) => ({
			leaderboardId: leaderboard.id,
			userId: user.userId,
			rank: index + 1,
			score: user.score,
			metadata: user.metadata,
		}));

		await this.getLeaderboardRepository().updateEntries(leaderboard.id, entries);

		// Clear cache
		const cachePattern = `leaderboard:${category}:${period}:*`;
		await this.getCacheService().deletePattern(cachePattern);
	}

	private getScoreForCategory(
		stats: UserLeaderboardStats,
		category: LeaderboardCategory
	): number {
		switch (category) {
			case LeaderboardCategory.TOTAL_POINTS:
				return stats.totalPoints;
			case LeaderboardCategory.WEEKLY_POINTS:
				return stats.weeklyPoints;
			case LeaderboardCategory.MONTHLY_POINTS:
				return stats.monthlyPoints;
			case LeaderboardCategory.CURRENT_STREAK:
				return stats.currentStreak;
			case LeaderboardCategory.LONGEST_STREAK:
				return stats.longestStreak;
			case LeaderboardCategory.WORDS_LEARNED:
				return stats.wordsLearned;
			case LeaderboardCategory.PARAGRAPHS_COMPLETED:
				return stats.paragraphsCompleted;
			case LeaderboardCategory.ACCURACY:
				return Math.round(stats.averageAccuracy * 100);
			case LeaderboardCategory.TIME_SPENT:
				return stats.totalTimeSpent;
			default:
				return 0;
		}
	}

	private isValidCategoryPeriodCombination(
		category: LeaderboardCategory,
		period: LeaderboardPeriod
	): boolean {
		// Weekly/Monthly points only make sense for weekly/monthly periods
		if (category === LeaderboardCategory.WEEKLY_POINTS && period !== LeaderboardPeriod.WEEKLY) {
			return false;
		}
		if (
			category === LeaderboardCategory.MONTHLY_POINTS &&
			period !== LeaderboardPeriod.MONTHLY
		) {
			return false;
		}
		return true;
	}
}
```

#### Repositories

**Leaderboard Repository** (`src/backend/repositories/leaderboard.repository.ts`)

```typescript
export interface LeaderboardRepository extends BaseRepository<Leaderboard> {
	findByCategoryAndPeriod(
		category: LeaderboardCategory,
		period: LeaderboardPeriod
	): Promise<Leaderboard | null>;
	getEntries(leaderboardId: string, limit: number, offset?: number): Promise<LeaderboardEntry[]>;
	getUserEntry(leaderboardId: string, userId: string): Promise<LeaderboardEntry | null>;
	updateEntries(leaderboardId: string, entries: Partial<LeaderboardEntry>[]): Promise<void>;
	getParticipantCount(leaderboardId: string): Promise<number>;
	getUsersAroundRank(
		leaderboardId: string,
		rank: number,
		range: number
	): Promise<LeaderboardEntry[]>;
}
```

### Frontend Implementation

#### Components

**Leaderboard Display Component** (`src/components/ui/leaderboard-display.tsx`)

```typescript
interface LeaderboardDisplayProps {
  category: LeaderboardCategory;
  period: LeaderboardPeriod;
  entries: LeaderboardEntry[];
  userRank?: UserRankInfo;
  loading?: boolean;
}

export function LeaderboardDisplay({
  category,
  period,
  entries,
  userRank,
  loading
}: LeaderboardDisplayProps) {
  const { t } = useTranslation();

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 2: return <Medal className="w-5 h-5 text-gray-400" />;
      case 3: return <Award className="w-5 h-5 text-amber-600" />;
      default: return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankChange = (entry: LeaderboardEntry) => {
    if (!entry.previousRank) return null;
    const change = entry.previousRank - entry.rank;
    if (change > 0) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (change < 0) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return <Minus className="w-4 h-4 text-gray-400" />;
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg animate-pulse">
            <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
              <div className="h-3 bg-gray-300 rounded w-1/4"></div>
            </div>
            <div className="h-6 bg-gray-300 rounded w-16"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* User's rank display */}
      {userRank && userRank.rank && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                #{userRank.rank}
              </div>
              <div>
                <p className="font-medium text-blue-900">Your Rank</p>
                <p className="text-sm text-blue-600">
                  {userRank.percentile}th percentile
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-bold text-blue-900">{userRank.score.toLocaleString()}</p>
              <p className="text-xs text-blue-600">
                {userRank.totalParticipants.toLocaleString()} participants
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard entries */}
      {entries.map((entry, index) => (
        <div
          key={entry.id}
          className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
            entry.rank <= 3
              ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200'
              : 'bg-white border border-gray-200 hover:bg-gray-50'
          }`}
        >
          <div className="flex items-center space-x-2">
            {getRankIcon(entry.rank)}
            {getRankChange(entry)}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <UserAvatar userId={entry.userId} size="sm" />
              <div>
                <p className="font-medium text-gray-900 truncate">
                  <UserDisplayName userId={entry.userId} />
                </p>
                {entry.metadata && (
                  <p className="text-xs text-gray-500">
                    {formatMetadata(entry.metadata, category)}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="text-right">
            <p className="font-bold text-gray-900">
              {entry.score.toLocaleString()}
            </p>
            <p className="text-xs text-gray-500">
              {formatCategoryUnit(category)}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
```

**Leaderboard Tabs Component** (`src/components/ui/leaderboard-tabs.tsx`)

```typescript
interface LeaderboardTabsProps {
  selectedCategory: LeaderboardCategory;
  selectedPeriod: LeaderboardPeriod;
  onCategoryChange: (category: LeaderboardCategory) => void;
  onPeriodChange: (period: LeaderboardPeriod) => void;
}

export function LeaderboardTabs({
  selectedCategory,
  selectedPeriod,
  onCategoryChange,
  onPeriodChange
}: LeaderboardTabsProps) {
  const categories = [
    { value: LeaderboardCategory.TOTAL_POINTS, label: 'Total Points', icon: Star },
    { value: LeaderboardCategory.CURRENT_STREAK, label: 'Current Streak', icon: Flame },
    { value: LeaderboardCategory.WORDS_LEARNED, label: 'Words Learned', icon: BookOpen },
    { value: LeaderboardCategory.ACCURACY, label: 'Accuracy', icon: Target },
  ];

  const periods = [
    { value: LeaderboardPeriod.WEEKLY, label: 'This Week' },
    { value: LeaderboardPeriod.MONTHLY, label: 'This Month' },
    { value: LeaderboardPeriod.ALL_TIME, label: 'All Time' },
  ];

  return (
    <div className="space-y-4">
      {/* Category tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
        {categories.map(({ value, label, icon: Icon }) => (
          <button
            key={value}
            onClick={() => onCategoryChange(value)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              selectedCategory === value
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span className="hidden sm:inline">{label}</span>
          </button>
        ))}
      </div>

      {/* Period tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
        {periods.map(({ value, label }) => (
          <button
            key={value}
            onClick={() => onPeriodChange(value)}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === value
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );
}
```

#### Hooks

**Leaderboard Hook** (`src/hooks/use-leaderboard.ts`)

```typescript
export function useLeaderboard() {
	const [selectedCategory, setSelectedCategory] = useState<LeaderboardCategory>(
		LeaderboardCategory.TOTAL_POINTS
	);
	const [selectedPeriod, setSelectedPeriod] = useState<LeaderboardPeriod>(
		LeaderboardPeriod.WEEKLY
	);
	const [entries, setEntries] = useState<LeaderboardEntry[]>([]);
	const [userRank, setUserRank] = useState<UserRankInfo | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const fetchLeaderboard = useCallback(
		async (category: LeaderboardCategory, period: LeaderboardPeriod) => {
			setLoading(true);
			setError(null);
			try {
				const [leaderboardData, rankData] = await Promise.all([
					getLeaderboardApi(category, period),
					getUserRankApi(category, period),
				]);
				setEntries(leaderboardData);
				setUserRank(rankData);
			} catch (err) {
				setError(err instanceof Error ? err : new Error('Failed to fetch leaderboard'));
			} finally {
				setLoading(false);
			}
		},
		[]
	);

	useEffect(() => {
		fetchLeaderboard(selectedCategory, selectedPeriod);
	}, [selectedCategory, selectedPeriod, fetchLeaderboard]);

	return {
		selectedCategory,
		selectedPeriod,
		entries,
		userRank,
		loading,
		error,
		setSelectedCategory,
		setSelectedPeriod,
		refreshLeaderboard: () => fetchLeaderboard(selectedCategory, selectedPeriod),
	};
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema implementation
- Basic leaderboard service and repository
- User stats tracking system

### Phase 2 (Weeks 3-4): Leaderboard Logic

- Ranking calculation algorithms
- Periodic leaderboard updates
- Caching implementation

### Phase 3 (Weeks 5-6): Frontend Components

- Leaderboard display components
- Category and period selection
- User rank visualization

### Phase 4 (Weeks 7-8): Advanced Features

- Real-time updates
- Social features integration
- Performance optimization

## Success Metrics

- User engagement with leaderboards
- Competition participation rates
- Social interaction increase
- Learning motivation improvement
- Daily/weekly active user growth

## Future Enhancements

- Custom leaderboard creation
- Team-based competitions
- Seasonal tournaments
- Achievement-based leaderboards
- Regional/language-specific boards
