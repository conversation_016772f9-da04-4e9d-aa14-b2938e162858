# Hệ Thống Caching - Tài Liệu Toàn Diện

## Tổng Quan Hệ Thống

Vocab sử dụng một hệ thống caching đa tầng tiên tiến để tối ưu hóa hiệ<PERSON> suất, gi<PERSON>m chi phí LLM và cải thiện trải nghiệm người dùng. Hệ thống bao gồm multiple cache providers, semantic caching, và intelligent cache warming strategies.

## Kiến Trúc Hệ Thống

### 1. C<PERSON>u Trúc Th<PERSON>

```
src/
├── backend/
│   ├── services/
│   │   ├── cache.service.ts              # NodeCache implementation
│   │   ├── redis-cache.service.ts        # Redis implementation
│   │   ├── semantic-cache.service.ts     # Semantic similarity caching
│   │   ├── cache-factory.service.ts      # Cache provider factory
│   │   └── server-cache.service.ts       # Server-side wrapper
│   ├── cache-init.server.ts              # Cache initialization
│   └── wire.ts                           # Dependency injection
├── config/
│   └── server.config.ts                  # Cache configuration
├── app/api/cache/                        # Cache management endpoints
│   ├── stats/                           # Cache statistics
│   ├── health/                          # Health checks
│   ├── warm/                            # Cache warming
│   ├── clear/                           # Cache clearing
│   └── optimize/                        # Cache optimization
└── components/dashboard/
    └── cache-performance.tsx             # Cache monitoring UI
```

### 2. Cache Providers

#### NodeCache (Default)

-   **In-memory caching** với NodeCache library
-   **TTL-based expiration** với automatic cleanup
-   **Tag-based invalidation** cho selective clearing
-   **Hit/miss statistics** tracking
-   **Priority-based TTL adjustment**

#### Redis Cache (Production)

-   **Distributed caching** với Redis
-   **Persistent storage** across server restarts
-   **Cluster support** cho high availability
-   **Advanced data structures** (sets, hashes, lists)
-   **Pub/Sub capabilities** cho cache invalidation

#### Semantic Cache (AI-Powered)

-   **Intelligent similarity matching** cho LLM responses
-   **Keyword-based indexing** với semantic search
-   **Access pattern optimization** dựa trên usage
-   **Similarity threshold configuration** (default: 0.8)
-   **Multi-dimensional similarity scoring**

## Cache Configuration

### Environment Variables

```bash
# Cache Provider Selection
USE_REDIS_CACHE=true                    # Enable Redis cache
REDIS_URL=redis://localhost:6379       # Redis connection string

# LLM Caching Configuration
LLM_CACHING_ENABLED=true               # Enable LLM response caching
LLM_CACHE_TTL_VOCABULARY=604800        # 7 days (vocabulary)
LLM_CACHE_TTL_WORD_DETAILS=604800      # 7 days (word details)
LLM_CACHE_TTL_PARAGRAPHS=259200        # 3 days (paragraphs)
LLM_CACHE_TTL_QUESTIONS=259200         # 3 days (questions)
LLM_CACHE_TTL_EVALUATIONS=2592000      # 30 days (evaluations)
LLM_CACHE_TTL_GRAMMAR=86400            # 1 day (grammar practice)

# Semantic Cache Configuration
LLM_SEMANTIC_CACHE_ENABLED=true        # Enable semantic similarity
LLM_SEMANTIC_CACHE_THRESHOLD=0.8       # Similarity threshold (0.0-1.0)
```

### TTL Strategy

```typescript
const CACHE_TTL_STRATEGY = {
	// Stable content - longer TTL
	vocabulary: 7 * 24 * 60 * 60, // 7 days
	wordDetails: 7 * 24 * 60 * 60, // 7 days
	evaluations: 30 * 24 * 60 * 60, // 30 days

	// Semi-dynamic content - medium TTL
	paragraphs: 3 * 24 * 60 * 60, // 3 days
	questions: 3 * 24 * 60 * 60, // 3 days

	// Dynamic content - short TTL
	grammarPractice: 1 * 24 * 60 * 60, // 1 day

	// Default fallback
	default: 24 * 60 * 60, // 24 hours
};
```

### Priority-Based TTL Adjustment

```typescript
const PRIORITY_MULTIPLIERS = {
	high: 1.5, // 150% of base TTL
	normal: 1.0, // 100% of base TTL
	low: 0.5, // 50% of base TTL
};
```

## Core Services API

### ICacheService Interface

```typescript
interface ICacheService {
	// Basic operations
	get<T>(key: string): Promise<T | null>;
	set<T>(key: string, value: T, ttl?: number): Promise<boolean>;
	del(key: string): Promise<boolean>;
	flush(): Promise<void>;

	// Optimized operations
	getOptimized<T>(key: string): Promise<T | null>;
	setOptimized<T>(key: string, value: T, options?: OptimizedCacheOptions): Promise<boolean>;

	// Management operations
	getStats(): Promise<CacheStats>;
	invalidateByTag(tag: string): Promise<number>;

	// Key generation utilities
	generateLLMKey(operation: string, params: Record<string, any>): string;
	generateKey(prefix: string, params: Record<string, any>): string;
}
```

### OptimizedCacheOptions

```typescript
interface OptimizedCacheOptions {
	ttl?: number; // Time to live in seconds
	tags?: string[]; // Tags for invalidation
	priority?: 'high' | 'normal' | 'low'; // Priority level
	compress?: boolean; // Enable compression (future)
}
```

### CacheStats Interface

```typescript
interface CacheStats {
	hits: number; // Cache hits count
	misses: number; // Cache misses count
	keys: number; // Total keys count
	hitRate: number; // Hit rate (0.0-1.0)
	memoryUsage: number; // Memory usage in bytes
	expiredEntries?: number; // Expired entries count
	totalKeywords?: number; // Semantic cache keywords
}
```

## Cache Key Strategies

### LLM Cache Keys

```typescript
// Vocabulary generation
const vocabKey = generateLLMKey('vocabulary', {
	keywords: ['technology', 'business'],
	language: 'EN',
	targetLanguage: 'VI',
	maxTerms: 10,
});
// Result: "llm:vocabulary:technology-business:EN:VI:10:hash"

// Word details
const detailsKey = generateLLMKey('wordDetails', {
	terms: ['computer', 'software'],
	sourceLanguage: 'EN',
	targetLanguage: 'VI',
});
// Result: "llm:wordDetails:computer-software:EN:VI:hash"

// Paragraph generation
const paragraphKey = generateLLMKey('paragraph', {
	keywords: ['education'],
	language: 'EN',
	difficulty: 'INTERMEDIATE',
	count: 1,
});
// Result: "llm:paragraph:education:EN:INTERMEDIATE:1:hash"
```

### Semantic Cache Keys

```typescript
// Semantic key generation
const semanticKey = generateSemanticKey({
	operation: 'generateWords',
	keywords: ['technology', 'innovation'],
	language: 'EN',
	difficulty: 'ADVANCED',
});
// Result: "semantic:generateWords:technology-innovation:EN:ADVANCED"

// Keyword extraction
const keywords = extractKeywords(key, params);
// Result: ['technology', 'innovation', 'EN', 'ADVANCED', 'generateWords']
```

## Semantic Caching System

### Similarity Calculation

```typescript
interface SimilarityCalculation {
	keywordSimilarity: number; // Keyword overlap score
	structuralSimilarity: number; // Parameter structure similarity
	semanticSimilarity: number; // Semantic meaning similarity
	combinedScore: number; // Weighted combined score
}

const SIMILARITY_WEIGHTS = {
	keywordWeight: 0.4, // 40% keyword similarity
	structuralWeight: 0.3, // 30% structural similarity
	semanticWeight: 0.3, // 30% semantic similarity
};
```

### Semantic Search Process

1. **Exact Match Check**: Tìm exact cache key trước
2. **Keyword Indexing**: Search theo keyword overlap
3. **Structural Analysis**: So sánh parameter structure
4. **Semantic Scoring**: Tính toán similarity score
5. **Threshold Filtering**: Chỉ return nếu score >= threshold
6. **Access Tracking**: Update access count cho optimization

## Cache Warming Strategies

### User-Specific Warming

```typescript
interface UserCacheWarming {
	// Warm user's collections
	warmUserCollections(userId: string): Promise<void>;

	// Warm user's recent words
	warmUserWords(userId: string, limit?: number): Promise<void>;

	// Warm user's practice history
	warmUserPractice(userId: string): Promise<void>;

	// Warm user's preferences
	warmUserPreferences(userId: string): Promise<void>;
}
```

### Content-Specific Warming

```typescript
interface ContentCacheWarming {
	// Warm popular vocabulary
	warmPopularWords(language: Language, limit?: number): Promise<void>;

	// Warm trending collections
	warmTrendingCollections(timeframe: string): Promise<void>;

	// Warm recent content
	warmRecentContent(hours: number): Promise<void>;

	// Warm by difficulty level
	warmByDifficulty(difficulty: Difficulty): Promise<void>;
}
```

### Scheduled Warming

```typescript
interface ScheduledWarming {
	// Daily warming tasks
	dailyWarmingJob(): Promise<void>;

	// Weekly optimization
	weeklyOptimizationJob(): Promise<void>;

	// Peak hours preparation
	peakHoursWarmingJob(): Promise<void>;

	// Cleanup expired entries
	cleanupExpiredJob(): Promise<void>;
}
```

## Performance Optimization

### Multi-Tier Caching

```typescript
interface CacheTiers {
	// L1: Application memory cache
	l1Cache: NodeCache;

	// L2: Distributed Redis cache
	l2Cache: RedisCache;

	// L3: Semantic similarity cache
	l3Cache: SemanticCache;

	// Cache coordination
	getCascading<T>(key: string): Promise<T | null>;
	setCascading<T>(key: string, value: T, options?: CacheOptions): Promise<boolean>;
}
```

### Cache Optimization Techniques

#### 1. Intelligent Prefetching

-   **Predictive Loading**: Dự đoán content user sẽ cần
-   **Batch Prefetching**: Load multiple related items cùng lúc
-   **Background Warming**: Warm cache trong background tasks

#### 2. Compression & Serialization

-   **JSON Compression**: Compress large JSON responses
-   **Binary Serialization**: Sử dụng binary format cho performance
-   **Selective Compression**: Chỉ compress items > threshold size

#### 3. Cache Partitioning

-   **User-Based Partitioning**: Separate cache per user/tenant
-   **Content-Type Partitioning**: Different strategies per content type
-   **Geographic Partitioning**: Regional cache distribution

## Monitoring & Analytics

### Cache Performance Metrics

```typescript
interface CacheMetrics {
	// Basic metrics
	hitRate: number; // Overall hit rate
	missRate: number; // Overall miss rate
	avgResponseTime: number; // Average response time

	// Advanced metrics
	hitRateByType: Record<string, number>; // Hit rate per content type
	memoryUsageByType: Record<string, number>; // Memory usage per type
	evictionRate: number; // Cache eviction rate

	// Semantic cache metrics
	semanticHitRate: number; // Semantic similarity hit rate
	avgSimilarityScore: number; // Average similarity score
	keywordIndexSize: number; // Keyword index size

	// Performance metrics
	cacheLatency: number; // Cache operation latency
	throughput: number; // Operations per second
	errorRate: number; // Cache error rate
}
```

### Real-time Monitoring

```typescript
interface CacheMonitoring {
	// Real-time stats
	getCurrentStats(): Promise<CacheMetrics>;

	// Historical data
	getHistoricalStats(timeframe: string): Promise<CacheMetrics[]>;

	// Alerts and notifications
	checkCacheHealth(): Promise<HealthStatus>;
	getPerformanceAlerts(): Promise<Alert[]>;

	// Optimization recommendations
	getOptimizationSuggestions(): Promise<OptimizationSuggestion[]>;
}
```

## Error Handling & Resilience

### Graceful Degradation

```typescript
interface CacheResilience {
	// Fallback strategies
	fallbackToSecondaryCache(key: string): Promise<any>;
	fallbackToDatabase(key: string): Promise<any>;

	// Circuit breaker pattern
	isCircuitOpen(): boolean;
	recordSuccess(): void;
	recordFailure(): void;

	// Retry mechanisms
	retryWithBackoff<T>(operation: () => Promise<T>, maxRetries: number): Promise<T>;
}
```

### Error Recovery

```typescript
interface ErrorRecovery {
	// Connection recovery
	reconnectToRedis(): Promise<boolean>;
	validateCacheConnection(): Promise<boolean>;

	// Data recovery
	recoverFromBackup(): Promise<void>;
	rebuildCacheIndex(): Promise<void>;

	// Health checks
	performHealthCheck(): Promise<HealthCheckResult>;
	autoHealCache(): Promise<void>;
}
```

## API Routes Documentation

### Cache Management Endpoints

#### GET /api/cache/stats

**Mô tả**: Lấy thống kê cache performance

**Query Parameters**:

-   `timeframe?: string` - Khung thời gian (hour, day, week, month)
-   `type?: string` - Loại cache (regular, semantic, combined)

**Response**:

```typescript
{
	success: true;
	data: {
		regular: {
			hits: number;
			misses: number;
			hitRate: number;
			keys: number;
			memoryUsage: number;
		}
		semantic: {
			hits: number;
			misses: number;
			hitRate: number;
			totalKeywords: number;
			avgSimilarityScore: number;
		}
		combined: {
			totalHits: number;
			totalMisses: number;
			overallHitRate: number;
			expiredEntries: number;
			totalMemoryUsage: number;
		}
	}
}
```

#### GET /api/cache/health

**Mô tả**: Kiểm tra health status của cache system

**Response**:

```typescript
{
	success: true;
	data: {
		status: 'healthy' | 'degraded' | 'unhealthy';
		providers: {
			nodeCache: {
				status: string;
				latency: number;
			}
			redis: {
				status: string;
				latency: number;
				connected: boolean;
			}
			semantic: {
				status: string;
				indexSize: number;
			}
		}
		uptime: number;
		lastCheck: string;
	}
}
```

#### POST /api/cache/warm

**Mô tả**: Trigger cache warming operations

**Request Body**:

```typescript
{
  strategy: 'user' | 'content' | 'popular' | 'recent';
  params?: {
    userId?: string;
    contentType?: string;
    language?: 'EN' | 'VI';
    limit?: number;
  };
}
```

**Response**:

```typescript
{
	success: true;
	data: {
		strategy: string;
		itemsWarmed: number;
		duration: number;
		cacheHitImprovement: number;
	}
}
```

#### POST /api/cache/clear

**Mô tả**: Clear cache entries

**Request Body**:

```typescript
{
  scope: 'all' | 'expired' | 'tag' | 'pattern';
  target?: string;  // Tag name or pattern
  confirm?: boolean; // Required for 'all' scope
}
```

#### GET /api/cache/optimize

**Mô tả**: Lấy optimization recommendations

**Response**:

```typescript
{
	success: true;
	data: {
		recommendations: Array<{
			type: 'ttl_adjustment' | 'warming_strategy' | 'eviction_policy';
			priority: 'high' | 'medium' | 'low';
			description: string;
			expectedImprovement: number;
			implementation: string;
		}>;
		currentEfficiency: number;
		potentialImprovement: number;
	}
}
```

### Cache Warming Jobs

#### POST /api/cache/warming/jobs

**Mô tả**: Schedule cache warming jobs

**Request Body**:

```typescript
{
  jobType: 'daily' | 'weekly' | 'peak_hours' | 'custom';
  schedule?: string; // Cron expression for custom jobs
  config: {
    warmingStrategies: string[];
    priority: 'high' | 'normal' | 'low';
    maxDuration: number; // Max duration in minutes
  };
}
```

## Ví Dụ Sử Dụng Thực Tế

### 1. Basic Cache Operations

```typescript
// Service layer usage
import { getCacheService } from '@/backend/cache-init.server';

class VocabularyService {
	private cacheService: ICacheService;

	async getWordDetails(terms: string[], language: Language) {
		const cacheKey = this.cacheService.generateLLMKey('wordDetails', {
			terms,
			language,
			timestamp: Date.now(),
		});

		// Try cache first
		const cached = await this.cacheService.getOptimized<WordDetail[]>(cacheKey);
		if (cached) {
			return cached;
		}

		// Generate new content
		const wordDetails = await this.llmService.generateWordDetails(terms, language);

		// Cache with optimized options
		await this.cacheService.setOptimized(cacheKey, wordDetails, {
			ttl: CacheService.getOptimizedTTL('wordDetails'),
			tags: ['vocabulary', `language:${language}`],
			priority: 'high',
		});

		return wordDetails;
	}
}
```

### 2. Semantic Cache Usage

```typescript
// Semantic similarity caching
import { SemanticCacheService } from '@/backend/services/semantic-cache.service';

class LLMService {
	private semanticCache = new SemanticCacheService();

	async generateParagraph(params: ParagraphParams) {
		const cacheKey = `paragraph:${JSON.stringify(params)}`;

		// Try semantic similarity search
		const similar = await this.semanticCache.getWithSemantic<Paragraph>(
			cacheKey,
			params,
			0.85 // Higher threshold for paragraphs
		);

		if (similar && similar.similarity > 0.85) {
			console.log(`Semantic cache hit: ${similar.similarity.toFixed(2)} similarity`);
			return similar.entry.value;
		}

		// Generate new paragraph
		const paragraph = await this.openAIService.generateParagraph(params);

		// Cache with semantic indexing
		await this.semanticCache.setWithSemantic(cacheKey, paragraph, params, {
			ttl: CacheService.getOptimizedTTL('paragraphs'),
			tags: ['llm', 'paragraph', `language:${params.language}`],
		});

		return paragraph;
	}
}
```

### 3. Cache Warming Implementation

```typescript
// Cache warming service
class CacheWarmingService {
	async warmUserCache(userId: string) {
		const user = await this.userService.getUserById(userId);
		if (!user) return;

		// Warm user's collections
		const collections = await this.collectionService.getUserCollections(userId);
		for (const collection of collections) {
			const cacheKey = `collection:${collection.id}:words`;
			const words = await this.wordService.getCollectionWords(collection.id);

			await this.cacheService.setOptimized(cacheKey, words, {
				ttl: 3600, // 1 hour for user-specific data
				tags: [`user:${userId}`, 'collections'],
				priority: 'normal',
			});
		}

		// Warm user's recent practice data
		const recentPractice = await this.practiceService.getRecentPractice(userId, 30);
		await this.cacheService.setOptimized(`practice:recent:${userId}`, recentPractice, {
			ttl: 1800, // 30 minutes
			tags: [`user:${userId}`, 'practice'],
		});
	}

	async warmPopularContent() {
		// Warm popular words
		const popularWords = await this.analyticsService.getPopularWords(100);
		await this.cacheService.setOptimized('popular:words', popularWords, {
			ttl: 7200, // 2 hours
			tags: ['popular', 'words'],
			priority: 'high',
		});

		// Warm trending collections
		const trendingCollections = await this.analyticsService.getTrendingCollections(50);
		await this.cacheService.setOptimized('trending:collections', trendingCollections, {
			ttl: 3600, // 1 hour
			tags: ['trending', 'collections'],
		});
	}
}
```

### 4. Cache Monitoring Dashboard

```typescript
// React component for cache monitoring
import { useCacheStats } from '@/hooks/use-cache-stats';

function CacheMonitoringDashboard() {
	const { stats, isLoading, refresh } = useCacheStats();

	const handleWarmCache = async (strategy: string) => {
		try {
			const response = await fetch('/api/cache/warm', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ strategy }),
			});

			const result = await response.json();
			toast.success(`Cache warmed: ${result.data.itemsWarmed} items`);
			refresh();
		} catch (error) {
			toast.error('Failed to warm cache');
		}
	};

	const handleClearExpired = async () => {
		try {
			await fetch('/api/cache/clear', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ scope: 'expired' }),
			});

			toast.success('Expired entries cleared');
			refresh();
		} catch (error) {
			toast.error('Failed to clear cache');
		}
	};

	if (isLoading) return <CacheStatsLoading />;

	return (
		<div className="space-y-6">
			{/* Cache Overview Cards */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<CacheStatsCard
					title="Hit Rate"
					value={`${(stats.combined.overallHitRate * 100).toFixed(1)}%`}
					trend={stats.hitRateTrend}
					color={stats.combined.overallHitRate > 0.8 ? 'green' : 'yellow'}
				/>

				<CacheStatsCard
					title="Memory Usage"
					value={formatBytes(stats.combined.totalMemoryUsage)}
					subtitle={`${stats.combined.totalKeys} keys`}
				/>

				<CacheStatsCard
					title="Semantic Efficiency"
					value={`${(stats.semantic.hitRate * 100).toFixed(1)}%`}
					subtitle={`${stats.semantic.totalKeywords} keywords indexed`}
				/>
			</div>

			{/* Cache Actions */}
			<div className="flex gap-4">
				<Button onClick={() => handleWarmCache('popular')}>Warm Popular Content</Button>
				<Button onClick={() => handleWarmCache('recent')}>Warm Recent Content</Button>
				<Button onClick={handleClearExpired} variant="outline">
					Clear Expired
				</Button>
			</div>

			{/* Detailed Stats */}
			<CachePerformanceChart data={stats.historical} />
		</div>
	);
}
```

### 5. Cache Invalidation Strategies

```typescript
// Event-driven cache invalidation
class CacheInvalidationService {
	async onCollectionUpdated(collectionId: string) {
		// Invalidate collection-specific caches
		await this.cacheService.invalidateByTag(`collection:${collectionId}`);

		// Invalidate related user caches
		const collection = await this.collectionService.getCollectionById(collectionId);
		if (collection) {
			await this.cacheService.invalidateByTag(`user:${collection.userId}`);
		}

		// Invalidate popular/trending caches if public collection
		if (collection?.isPublic) {
			await this.cacheService.invalidateByTag('popular');
			await this.cacheService.invalidateByTag('trending');
		}
	}

	async onWordAdded(wordId: string, collectionId: string) {
		// Invalidate word-related caches
		await this.cacheService.invalidateByTag(`word:${wordId}`);
		await this.cacheService.invalidateByTag(`collection:${collectionId}`);

		// Invalidate LLM caches that might include this word
		await this.cacheService.invalidateByTag('llm:vocabulary');
		await this.cacheService.invalidateByTag('llm:wordDetails');
	}

	async onUserPreferencesChanged(userId: string) {
		// Invalidate all user-specific caches
		await this.cacheService.invalidateByTag(`user:${userId}`);

		// Invalidate personalized content caches
		await this.cacheService.invalidateByTag(`personalized:${userId}`);
	}
}
```

## Best Practices

### 1. Cache Key Design

-   **Hierarchical Structure**: Sử dụng consistent naming convention
-   **Versioning**: Include version trong cache key khi cần
-   **Parameterization**: Include all relevant parameters
-   **Hashing**: Hash long parameters để avoid key length limits

### 2. TTL Strategy

-   **Content-Based TTL**: Different TTL cho different content types
-   **Priority-Based Adjustment**: Adjust TTL based on content priority
-   **Dynamic TTL**: Adjust TTL based on access patterns
-   **Graceful Expiration**: Implement soft expiration với background refresh

### 3. Memory Management

-   **Size Limits**: Set appropriate memory limits cho each cache tier
-   **Eviction Policies**: Implement LRU, LFU, hoặc custom eviction
-   **Compression**: Compress large cache entries
-   **Monitoring**: Monitor memory usage và set alerts

### 4. Error Handling

-   **Graceful Degradation**: Always have fallback strategies
-   **Circuit Breaker**: Implement circuit breaker cho cache failures
-   **Retry Logic**: Implement exponential backoff cho retries
-   **Health Checks**: Regular health checks cho cache providers

### 5. Security

-   **Access Control**: Implement proper access control cho cache operations
-   **Data Encryption**: Encrypt sensitive data trong cache
-   **Audit Logging**: Log cache operations cho security auditing
-   **Rate Limiting**: Implement rate limiting cho cache API endpoints

Tài liệu này cung cấp cái nhìn toàn diện về hệ thống caching của dự án Vocab, bao gồm architecture, implementation details, API documentation và best practices để optimize performance và reliability.
