# 🍞 Toast System Documentation

## 📋 Tổng Quan

Dự án sử dụng **Sonner Toast system** với enhanced context-based implementation:
1. **Sonner Toast** - Primary toast system với rich error handling
2. **Undo Toast** - Specialized toast system cho undo operations

## 🏗️ <PERSON>ến <PERSON>r<PERSON>c <PERSON>ệ Thống

### 1. **Sonner Toast System (Primary)**

#### **Context Provider**
```typescript
// src/contexts/toast-context.tsx
export interface ToastContextType {
  showError: (error: AppError | Error | string, options?: ToastErrorOptions) => void;
  showSuccess: (message: string, options?: ToastOptions) => void;
  showInfo: (message: string, description?: string, duration?: number) => void;
  showWarning: (message: string, description?: string, duration?: number) => void;
  showLoading: (message: string, description?: string) => string | number;
  showPromise: <T>(promise: Promise<T>, messages: {...}) => void;
  dismiss: (toastId?: string | number) => void;
  dismissAll: () => void;
}
```

#### **Error Integration**
- Tích hợp với `AppError` system
- Automatic error severity mapping
- Retry functionality cho failed operations
- Error ID display cho debugging

### 2. **Undo Toast System**

#### **Specialized Undo Toasts**
```typescript
// src/components/ui/undo-toast.tsx
- UndoToast: Individual undo toast component
- UndoToastManager: Manages multiple undo toasts
- useUndoToast(): Hook for undo toast management
- QuickUndoButton: Standalone undo button
```

## 🎨 Toast Variants & Styling

### **Radix UI Variants**
```typescript
// toast.tsx variants
variants: {
  variant: {
    default: 'border bg-background text-foreground',
    destructive: 'destructive group border-destructive bg-destructive text-destructive-foreground',
  },
}
```

### **Sonner Toast Types**
- `toast.success()` - Success notifications
- `toast.error()` - Error notifications  
- `toast.info()` - Information messages
- `toast.warning()` - Warning messages
- `toast.loading()` - Loading states
- `toast.promise()` - Promise-based toasts

### **Visual Positioning**
```css
/* Toast viewport positioning */
position: fixed;
top: 0; /* Mobile: top */
bottom: 0; /* Desktop: bottom */
right: 0; /* Desktop: right side */
max-width: 420px; /* Desktop max width */
z-index: 100; /* High z-index */
```

## 🔧 API Documentation

### **Radix UI Toast API**

#### **Basic Usage**
```typescript
import { useToast } from '@/components/ui';

function MyComponent() {
  const { toast } = useToast();
  
  const showToast = () => {
    toast({
      title: "Success!",
      description: "Your action was completed.",
      variant: "default", // or "destructive"
    });
  };
}
```

#### **Toast Options**
```typescript
interface ToastProps {
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
  variant?: "default" | "destructive";
  duration?: number;
  onOpenChange?: (open: boolean) => void;
}
```

#### **Toast Actions**
```typescript
toast({
  title: "Confirm Action",
  description: "Are you sure you want to continue?",
  action: (
    <ToastAction altText="Confirm" onClick={handleConfirm}>
      Confirm
    </ToastAction>
  ),
});
```

### **Sonner Toast API (Enhanced)**

#### **Basic Usage**
```typescript
import { useToast } from '@/contexts/toast-context';

function MyComponent() {
  const { showSuccess, showError, showInfo, showWarning } = useToast();
  
  // Success toast
  showSuccess("Operation completed successfully!");
  
  // Error toast with retry
  showError(error, {
    onRetry: async () => {
      await retryOperation();
    },
    showErrorId: true,
    duration: 5000
  });
  
  // Info toast
  showInfo("Information", "Additional details here", 3000);
  
  // Warning toast
  showWarning("Warning", "Please check your input", 4000);
}
```

#### **Loading & Promise Toasts**
```typescript
// Loading toast
const loadingId = showLoading("Processing...", "Please wait");

// Promise toast
showPromise(
  fetchData(),
  {
    loading: "Fetching data...",
    success: (data) => `Loaded ${data.length} items`,
    error: (error) => `Failed: ${error.message}`
  },
  {
    loadingDescription: "This may take a moment",
    successDescription: (data) => `Successfully processed ${data.length} items`,
    errorDescription: (error) => `Error details: ${error.code}`
  }
);
```

#### **Error Handling Integration**
```typescript
// Automatic error severity mapping
showError(new AppError("Network error", ErrorSeverity.HIGH), {
  onRetry: retryNetworkCall,
  showErrorId: true
});

// String error
showError("Simple error message");

// Native Error object
showError(new Error("Something went wrong"));
```

### **Undo Toast API**

#### **Basic Undo Toast**
```typescript
import { useUndoToast } from '@/components/ui/undo-toast';

function MyComponent() {
  const { showUndoToast, hideUndoToast } = useUndoToast();
  
  const handleDelete = async (item) => {
    await deleteItem(item.id);
    
    showUndoToast({
      id: `delete-${item.id}`,
      type: 'delete',
      description: `Deleted "${item.name}"`,
      data: item,
      timestamp: Date.now(),
      expiresAt: Date.now() + 10000, // 10 seconds
    });
  };
}
```

#### **Undo Toast Manager**
```typescript
import { UndoToastManager } from '@/components/ui/undo-toast';

function App() {
  const [undoActions, setUndoActions] = useState([]);
  
  const handleUndo = async (actionId: string) => {
    const action = undoActions.find(a => a.id === actionId);
    if (action) {
      await performUndo(action);
      setUndoActions(prev => prev.filter(a => a.id !== actionId));
      return true;
    }
    return false;
  };
  
  return (
    <UndoToastManager
      actions={undoActions}
      onUndo={handleUndo}
      onDismiss={(id) => setUndoActions(prev => prev.filter(a => a.id !== id))}
      maxVisible={3}
    />
  );
}
```

## 🌐 Internationalization

### **Toast Translations**
```typescript
// src/contexts/translations/toast.trans.ts
export const toastTranslations: TranslationDict = {
  'toast.feedback_sent': {
    EN: 'Feedback sent',
    VI: 'Đã gửi phản hồi',
  },
  'toast.feedback_sent_desc': {
    EN: 'Thank you for your feedback',
    VI: 'Cảm ơn phản hồi của bạn',
  },
  'toast.missing_info': {
    EN: 'Missing Information',
    VI: 'Thiếu thông tin',
  },
  'toast.missing_info_desc': {
    EN: 'Please fill in all required fields',
    VI: 'Vui lòng điền đầy đủ các trường bắt buộc',
  },
  'toast.submission_failed': {
    EN: 'Submission Failed',
    VI: 'Gửi thất bại',
  },
  'toast.submission_failed_desc': {
    EN: 'Failed to submit feedback. Please try again.',
    VI: 'Không thể gửi phản hồi. Vui lòng thử lại.',
  },
};
```

### **Usage with Translations**
```typescript
import { Translate } from '@/components/ui';

// Using translation keys
toast({
  title: <Translate text="toast.feedback_sent" />,
  description: <Translate text="toast.feedback_sent_desc" />,
});

// Or with translation function
const t = useTranslation();
showSuccess(t('toast.feedback_sent'));
```

## 🎯 Cách Sử Dụng Trong Dự Án

### **1. Setup trong Layout**
```typescript
// src/app/layout.tsx
import { ToastProvider } from '@/contexts/toast-context';
import { Toaster } from '@/components/ui';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <ToastProvider>
          {children}
          <Toaster /> {/* Radix UI Toaster */}
        </ToastProvider>
      </body>
    </html>
  );
}
```

### **2. Component Usage Examples**

#### **Form Submission**
```typescript
// src/app/collections/[id]/rename-collection-dialog.tsx
const { toast } = useToast();

const handleSubmit = async (data) => {
  try {
    await updateCollection(data);
    toast({
      title: "Collection renamed",
      description: `Successfully renamed to "${data.name}"`,
    });
  } catch (error) {
    toast({
      title: "Failed to rename collection",
      description: error.message,
      variant: "destructive",
    });
  }
};
```

#### **Word Generation**
```typescript
// src/app/collections/[id]/vocabulary/generate/generate-words-client.tsx
const { toast } = useToast();

const generateWords = async () => {
  try {
    const words = await generateWordsAPI(params);
    toast({
      title: `Generated ${words.length} words`,
      description: "Words have been added to your collection",
    });
  } catch (error) {
    toast({
      title: "Generation failed",
      description: "Please try again with different keywords",
      variant: "destructive",
    });
  }
};
```

#### **MCQ Practice**
```typescript
// src/app/collections/[id]/vocabulary/mcq/mcq-client.tsx
const { toast } = useToast();

const handleAnswer = (isCorrect: boolean) => {
  if (isCorrect) {
    toast({
      title: "Correct! 🎉",
      description: "Great job! Keep it up.",
    });
  } else {
    toast({
      title: "Not quite right",
      description: "Try again or check the explanation",
      variant: "destructive",
    });
  }
};
```

#### **Error Handling with Context**
```typescript
// Using enhanced toast context
const { showError, showSuccess, showPromise } = useToast();

// API call with promise toast
showPromise(
  saveData(formData),
  {
    loading: "Saving changes...",
    success: "Changes saved successfully!",
    error: "Failed to save changes"
  }
);

// Error with retry functionality
showError(error, {
  onRetry: async () => {
    await retryOperation();
  },
  showErrorId: true
});
```

### **3. Advanced Usage Patterns**

#### **Batch Operations**
```typescript
const { showLoading, dismiss, showSuccess, showError } = useToast();

const processBatch = async (items) => {
  const loadingId = showLoading(
    `Processing ${items.length} items...`,
    "This may take a moment"
  );
  
  try {
    const results = await Promise.allSettled(
      items.map(item => processItem(item))
    );
    
    dismiss(loadingId);
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.length - successful;
    
    if (failed === 0) {
      showSuccess(`Successfully processed all ${items.length} items`);
    } else {
      showWarning(
        `Processed ${successful}/${items.length} items`,
        `${failed} items failed to process`
      );
    }
  } catch (error) {
    dismiss(loadingId);
    showError(error);
  }
};
```

#### **Undo Operations**
```typescript
const { showUndoToast } = useUndoToast();

const deleteWord = async (word) => {
  // Perform delete
  await deleteWordAPI(word.id);
  
  // Show undo toast
  showUndoToast({
    id: `delete-word-${word.id}`,
    type: 'delete',
    description: `Deleted "${word.text}"`,
    data: { word },
    timestamp: Date.now(),
    expiresAt: Date.now() + 10000,
  });
};

const handleUndo = async (actionId: string) => {
  const action = getUndoAction(actionId);
  if (action?.type === 'delete') {
    await restoreWordAPI(action.data.word);
    return true;
  }
  return false;
};
```

## ⚙️ Configuration

### **Toast Limits & Timing**
```typescript
// src/components/ui/use-toast.ts
const TOAST_LIMIT = 1; // Maximum concurrent toasts
const TOAST_REMOVE_DELAY = 1000000; // Auto-remove delay (very long)

// Sonner toasts have different defaults
const DEFAULT_DURATION = 4000; // 4 seconds
const ERROR_DURATION = 6000; // 6 seconds for errors
const SUCCESS_DURATION = 3000; // 3 seconds for success
```

### **Styling Customization**
```css
/* Global toast styles in globals.css */
.toast-viewport {
  position: fixed;
  top: 0;
  z-index: 100;
  /* ... */
}

.toast {
  border-radius: 0.5rem;
  padding: 1.5rem;
  /* ... */
}
```

### **Animation Configuration**
```typescript
// Framer Motion animations for undo toasts
const toastAnimations = {
  initial: { opacity: 0, y: 50 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.2 }
};
```

## 🔍 Best Practices

### **1. Toast Type Selection**
- **Success**: Confirmations, completed actions
- **Error**: Failed operations, validation errors
- **Warning**: Important notices, potential issues
- **Info**: General information, tips
- **Loading**: Long-running operations

### **2. Message Guidelines**
- **Title**: Short, descriptive (2-4 words)
- **Description**: Clear explanation (1-2 sentences)
- **Actions**: Relevant follow-up actions (Retry, Undo, etc.)

### **3. Error Handling**
```typescript
// Good: Specific error messages
showError("Failed to save collection", {
  onRetry: () => saveCollection(),
  showErrorId: true
});

// Bad: Generic error messages
showError("Something went wrong");
```

### **4. Performance Considerations**
- Limit concurrent toasts (TOAST_LIMIT = 1)
- Use appropriate durations
- Clean up timeouts properly
- Avoid toast spam

### **5. Accessibility**
- Provide meaningful titles and descriptions
- Use appropriate ARIA labels
- Support keyboard navigation
- Consider screen reader announcements

## 🧪 Testing

### **Unit Testing**
```typescript
// Testing toast functionality
import { renderHook, act } from '@testing-library/react';
import { useToast } from '@/components/ui';

test('should create toast with correct properties', () => {
  const { result } = renderHook(() => useToast());
  
  act(() => {
    result.current.toast({
      title: "Test Toast",
      description: "Test Description"
    });
  });
  
  expect(result.current.toasts).toHaveLength(1);
  expect(result.current.toasts[0].title).toBe("Test Toast");
});
```

### **Integration Testing**
```typescript
// Testing toast in components
import { render, screen, fireEvent } from '@testing-library/react';
import { ToastProvider } from '@/contexts/toast-context';

test('should show success toast on form submission', async () => {
  render(
    <ToastProvider>
      <MyForm />
    </ToastProvider>
  );
  
  fireEvent.click(screen.getByRole('button', { name: /submit/i }));
  
  await screen.findByText('Form submitted successfully');
});
```

## 📚 Dependencies

### **Core Dependencies**
```json
{
  "@radix-ui/react-toast": "^1.x.x",
  "sonner": "^1.x.x",
  "class-variance-authority": "^0.x.x",
  "framer-motion": "^10.x.x",
  "lucide-react": "^0.x.x"
}
```

### **Internal Dependencies**
- `@/lib/utils` - Utility functions (cn)
- `@/lib/error-handling` - Error management
- `@/contexts/translations` - Internationalization
- `@/hooks/use-undo-actions` - Undo functionality

## 🔄 Migration Guide

### **From Basic Toast to Enhanced Toast**
```typescript
// Old way
import { toast } from 'react-hot-toast';
toast.success('Success!');

// New way
import { useToast } from '@/contexts/toast-context';
const { showSuccess } = useToast();
showSuccess('Success!');
```

### **Adding Error Handling**
```typescript
// Before
toast.error(error.message);

// After
showError(error, {
  onRetry: retryFunction,
  showErrorId: true
});
```

## 🎯 Kết Luận

Toast system trong dự án cung cấp:
- ✅ **Dual implementation** cho flexibility
- ✅ **Rich error handling** với retry functionality  
- ✅ **Undo operations** cho better UX
- ✅ **Internationalization** support
- ✅ **Type safety** với TypeScript
- ✅ **Accessibility** features
- ✅ **Customizable styling** với Tailwind CSS
- ✅ **Animation support** với Framer Motion

Hệ thống được thiết kế để handle mọi use case từ simple notifications đến complex error handling và undo operations.

---

**Last Updated**: December 2024  
**Version**: 2.0  
**Status**: Production Ready ✅