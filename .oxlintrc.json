{
	"$schema": "https://raw.githubusercontent.com/oxc-project/oxc/main/crates/oxc_linter/src/options/schema.json",
	"plugins": ["react", "unicorn", "typescript", "jsx-a11y"],
	"env": {
		"browser": true,
		"es2022": true,
		"node": true
	},
	"globals": {
		"React": "readonly",
		"JSX": "readonly"
	},
	"rules": {
		// Disable rules that conflict with TypeScript or are handled by other tools
		"no-unused-vars": "off",
		"no-undef": "off",

		// React specific rules
		"react/react-in-jsx-scope": "off",
		"react/prop-types": "off",
		"react/exhaustive-deps": "warn", // Make this a warning instead of error

		// TypeScript specific rules
		"typescript/no-unused-vars": "off",
		"typescript/no-explicit-any": "off",
		"typescript/no-empty-object-type": "off",

		// Unicorn rules for better code quality
		"unicorn/prefer-node-protocol": "error",
		"unicorn/prefer-module": "error",
		"unicorn/no-array-for-each": "off", // Disable this for now as it's too noisy
		"unicorn/prefer-array-some": "warn",
		"unicorn/no-empty-file": "warn",

		// JSX a11y rules for accessibility - make them warnings for now
		"jsx-a11y/alt-text": "warn",
		"jsx-a11y/anchor-has-content": "warn",
		"jsx-a11y/anchor-is-valid": "warn",
		"jsx-a11y/click-events-have-key-events": "off", // Too noisy for now
		"jsx-a11y/no-static-element-interactions": "off", // Too noisy for now
		"jsx-a11y/no-autofocus": "warn",
		"jsx-a11y/prefer-tag-over-role": "warn",
		"jsx-a11y/heading-has-content": "warn",
		"jsx-a11y/role-has-required-aria-props": "warn"
	},
	"settings": {
		"react": {
			"version": "detect"
		}
	}
}
