import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
	env: {},
	reactStrictMode: false,
	images: {
		unoptimized: true,
	},
	serverExternalPackages: ['ioredis', 'node-cache'],

	// Turbopack configuration for faster development builds
	turbopack: {
		// Configure resolve extensions (optional - these are defaults)
		resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.mjs', '.json'],

		// Configure webpack loaders if needed (currently none required for your setup)
		rules: {
			// Example: If you need SVG support as React components
			// '*.svg': {
			//   loaders: ['@svgr/webpack'],
			//   as: '*.js',
			// },
		},
	},

	webpack: (config, { isServer }) => {
		// Exclude server-only packages from client bundle
		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				net: false,
				dns: false,
				tls: false,
				crypto: false,
				ioredis: false,
				'node-cache': false,
			};

			// Additional optimization: exclude server-only modules
			config.resolve.alias = {
				...config.resolve.alias,
				'@/backend/services/redis-cache.service': false,
				'@/backend/cache-init.server': false,
			};
		}
		return config;
	},
};

export default nextConfig;
